(index):24 [EARLY] window.location.href: http://localhost:5173/
(index):25 [EARLY] window.location.search: 
(index):26 [EARLY] window.location.hash: 
(index):64 cdn.tailwindcss.com should not be used in production. To use Tailwind CSS in production, install it as a PostCSS plugin or use the Tailwind CLI: https://tailwindcss.com/docs/installation
(anonymous) @ (index):64
(anonymous) @ (index):64
client:789 [vite] connecting...
client:912 [vite] connected.
chunk-TH7NCS4R.js?v=ea90511a:21609 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
contentScript.bundle.js:31 getIdsFromUrl result - promptId: null jobId: null
contentScript.bundle.js:31 Checking domain: localhost
contentScript.bundle.js:31 🔍 Delay detection: URL parameter 'null', using 1000ms delay
contentScript.bundle.js:31 🚀 Queue initialized with delay feature active (1000ms between prompts)
contentScript.bundle.js:31 ⚠️ IMPORTANT: Make sure URL has delay parameter if you want to customize delay time
contentScript.bundle.js:31 ✅ Queue initialization complete - ready to process messages
i18n.ts:32 i18next: languageChanged en
i18n.ts:32 i18next: initialized {debug: true, initImmediate: true, ns: Array(1), defaultNS: Array(1), fallbackLng: Array(1), …}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: false, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: false, isAdmin: false, currentUser: {…}, userRole: undefined}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:221 🔍 [App] Starting connection monitoring and auth state monitoring
connectionService.ts:132 🔍 [ConnectionService] Starting health monitoring
userService.ts:135 🔍 [UserService] Initializing auth state monitoring
connectionService.ts:57 🔍 [ConnectionService] Checking database connection...
App.tsx:244 🔍 [App] Starting initial data fetch...
App.tsx:250 🔍 [App] Testing Supabase connection...
react-router-dom.js?v=ea90511a:4393 ⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition.
warnOnce @ react-router-dom.js?v=ea90511a:4393
logDeprecation @ react-router-dom.js?v=ea90511a:4396
logV6DeprecationWarnings @ react-router-dom.js?v=ea90511a:4399
(anonymous) @ react-router-dom.js?v=ea90511a:5271
commitHookEffectListMount @ chunk-TH7NCS4R.js?v=ea90511a:16963
commitPassiveMountOnFiber @ chunk-TH7NCS4R.js?v=ea90511a:18211
commitPassiveMountEffects_complete @ chunk-TH7NCS4R.js?v=ea90511a:18179
commitPassiveMountEffects_begin @ chunk-TH7NCS4R.js?v=ea90511a:18169
commitPassiveMountEffects @ chunk-TH7NCS4R.js?v=ea90511a:18159
flushPassiveEffectsImpl @ chunk-TH7NCS4R.js?v=ea90511a:19543
flushPassiveEffects @ chunk-TH7NCS4R.js?v=ea90511a:19500
(anonymous) @ chunk-TH7NCS4R.js?v=ea90511a:19381
workLoop @ chunk-TH7NCS4R.js?v=ea90511a:197
flushWork @ chunk-TH7NCS4R.js?v=ea90511a:176
performWorkUntilDeadline @ chunk-TH7NCS4R.js?v=ea90511a:384
react-router-dom.js?v=ea90511a:4393 ⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath.
warnOnce @ react-router-dom.js?v=ea90511a:4393
logDeprecation @ react-router-dom.js?v=ea90511a:4396
logV6DeprecationWarnings @ react-router-dom.js?v=ea90511a:4402
(anonymous) @ react-router-dom.js?v=ea90511a:5271
commitHookEffectListMount @ chunk-TH7NCS4R.js?v=ea90511a:16963
commitPassiveMountOnFiber @ chunk-TH7NCS4R.js?v=ea90511a:18211
commitPassiveMountEffects_complete @ chunk-TH7NCS4R.js?v=ea90511a:18179
commitPassiveMountEffects_begin @ chunk-TH7NCS4R.js?v=ea90511a:18169
commitPassiveMountEffects @ chunk-TH7NCS4R.js?v=ea90511a:18159
flushPassiveEffectsImpl @ chunk-TH7NCS4R.js?v=ea90511a:19543
flushPassiveEffects @ chunk-TH7NCS4R.js?v=ea90511a:19500
(anonymous) @ chunk-TH7NCS4R.js?v=ea90511a:19381
workLoop @ chunk-TH7NCS4R.js?v=ea90511a:197
flushWork @ chunk-TH7NCS4R.js?v=ea90511a:176
performWorkUntilDeadline @ chunk-TH7NCS4R.js?v=ea90511a:384
App.tsx:221 🔍 [App] Starting connection monitoring and auth state monitoring
connectionService.ts:132 🔍 [ConnectionService] Starting health monitoring
userService.ts:135 🔍 [UserService] Initializing auth state monitoring
connectionService.ts:36 🔍 [ConnectionService] Returning pending connection check
App.tsx:244 🔍 [App] Starting initial data fetch...
App.tsx:250 🔍 [App] Testing Supabase connection...
App.tsx:872 🔍 [App] Current state: {isAuthenticated: false, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: false, isAdmin: false, currentUser: {…}, userRole: undefined}
userService.ts:138 🔄 [UserService] Auth state changed: INITIAL_SESSION
userService.ts:126 🔄 [UserService] Clearing user cache and pending requests
userService.ts:138 🔄 [UserService] Auth state changed: INITIAL_SESSION
userService.ts:126 🔄 [UserService] Clearing user cache and pending requests
App.tsx:334 Auth state changed: INITIAL_SESSION <EMAIL>
dataService.ts:33 🔍 [DataService] Delegating to UserService.getCurrentUser
userService.ts:26 🔍 [UserService] getCurrentUser called
dataService.ts:257 🔍 [DataService] Delegating to ChatService.getChatSessions
connectionService.ts:36 🔍 [ConnectionService] Returning pending connection check
userService.ts:83 🔍 [UserService] Fetching profile for user: fc025615-93c4-4d2e-ba33-c32ad7edb30c
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: false, currentUser: {…}, userRole: undefined}
connectionService.ts:75 ✅ [ConnectionService] Database connection healthy
App.tsx:231 🔍 [App] Initial connection check: Connected
App.tsx:231 🔍 [App] Initial connection check: Connected
connectionService.ts:151 🔍 [ConnectionService] Executing getChatSessions with healthy connection
chatService.ts:24 🔍 [ChatService] getChatSessions called
connectionService.ts:201 🔍 [ConnectionService] Checking auth session...
connectionService.ts:242 ✅ [ConnectionService] Auth session is valid
chatService.ts:41 🔍 [ChatService] Fetching chat sessions for user: fc025615-93c4-4d2e-ba33-c32ad7edb30c
userService.ts:108 🔍 [UserService] Current user profile: {id: 'fc025615-93c4-4d2e-ba33-c32ad7edb30c', name: 'Sergey V. Ryzhkov', avatar_url: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD…7V4igK7eUqhwDt7MmZKKVd1lPiBIX6y0skEEY80P9xentR//Z', email: '<EMAIL>', created_at: '2025-07-14T07:18:15.062147+00:00', …}
App.tsx:258 ✅ [App] Supabase connection test successful
App.tsx:275 🔍 [App] Checking authentication status...
chatService.ts:54 🔍 [ChatService] Raw chat sessions: (2) [{…}, {…}]
chatService.ts:64 🔍 [ChatService] Converted chat sessions: (2) [{…}, {…}]
App.tsx:348 🔍 [App] Auth change - Admin status: true User role: admin
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:279 🔍 [App] Authentication status: true
App.tsx:289 🔍 [App] Fetching user profiles...
dataService.ts:33 🔍 [DataService] Delegating to UserService.getCurrentUser
userService.ts:26 🔍 [UserService] getCurrentUser called
userService.ts:30 🔍 [UserService] Returning cached user
App.tsx:291 🔍 [App] User profiles data: true
App.tsx:300 🔍 [App] Fetching chat sessions...
dataService.ts:257 🔍 [DataService] Delegating to ChatService.getChatSessions
connectionService.ts:151 🔍 [ConnectionService] Executing getChatSessions with healthy connection
chatService.ts:24 🔍 [ChatService] getChatSessions called
connectionService.ts:201 🔍 [ConnectionService] Checking auth session...
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
connectionService.ts:242 ✅ [ConnectionService] Auth session is valid
chatService.ts:41 🔍 [ChatService] Fetching chat sessions for user: fc025615-93c4-4d2e-ba33-c32ad7edb30c
App.tsx:258 ✅ [App] Supabase connection test successful
App.tsx:275 🔍 [App] Checking authentication status...
chatService.ts:54 🔍 [ChatService] Raw chat sessions: (2) [{…}, {…}]
chatService.ts:64 🔍 [ChatService] Converted chat sessions: (2) [{…}, {…}]
App.tsx:302 🔍 [App] Chat sessions data: 2
App.tsx:312 🔍 [App] Admin status: true User role: admin
App.tsx:316 ✅ [App] Initial data fetch completed successfully
App.tsx:325 🔍 [App] Setting dataLoading to false
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:279 🔍 [App] Authentication status: true
App.tsx:289 🔍 [App] Fetching user profiles...
dataService.ts:33 🔍 [DataService] Delegating to UserService.getCurrentUser
userService.ts:26 🔍 [UserService] getCurrentUser called
userService.ts:30 🔍 [UserService] Returning cached user
App.tsx:291 🔍 [App] User profiles data: true
App.tsx:300 🔍 [App] Fetching chat sessions...
dataService.ts:257 🔍 [DataService] Delegating to ChatService.getChatSessions
connectionService.ts:151 🔍 [ConnectionService] Executing getChatSessions with healthy connection
chatService.ts:24 🔍 [ChatService] getChatSessions called
connectionService.ts:201 🔍 [ConnectionService] Checking auth session...
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
connectionService.ts:242 ✅ [ConnectionService] Auth session is valid
chatService.ts:41 🔍 [ChatService] Fetching chat sessions for user: fc025615-93c4-4d2e-ba33-c32ad7edb30c
chatService.ts:54 🔍 [ChatService] Raw chat sessions: (2) [{…}, {…}]
chatService.ts:64 🔍 [ChatService] Converted chat sessions: (2) [{…}, {…}]
App.tsx:302 🔍 [App] Chat sessions data: 2
App.tsx:312 🔍 [App] Admin status: true User role: admin
App.tsx:316 ✅ [App] Initial data fetch completed successfully
App.tsx:325 🔍 [App] Setting dataLoading to false
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:467 🔍 [handleSelectChat] Chat selected: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:492 🔍 [handleSelectChat] Selected session: This is a new chat... Messages count: 0
App.tsx:496 🔍 [handleSelectChat] Loading messages for chat: bce18b79-966f-4868-8844-e67143528bb8
dataService.ts:268 🔍 [DataService] Delegating to ChatService.getMessagesForSession
contentScript.bundle.js:31 Unsupported domain for continue button
connectionService.ts:151 🔍 [ConnectionService] Executing getMessagesForSession(bce18b79-966f-4868-8844-e67143528bb8) with healthy connection
chatService.ts:76 🔍 [ChatService] getMessagesForSession called for: bce18b79-966f-4868-8844-e67143528bb8
connectionService.ts:201 🔍 [ConnectionService] Checking auth session...
connectionService.ts:242 ✅ [ConnectionService] Auth session is valid
chatService.ts:96 🔍 [ChatService] Raw messages: (11) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
chatService.ts:110 🔍 [ChatService] Converted messages: (11) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
App.tsx:521 ✅ [handleSelectChat] Messages loaded for chat: bce18b79-966f-4868-8844-e67143528bb8 Count: 11
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
[Violation] 'message' handler took <N>ms
contentScript.bundle.js:31 Unsupported domain for continue button
connectionService.ts:57 🔍 [ConnectionService] Checking database connection...
connectionService.ts:36 🔍 [ConnectionService] Returning pending connection check
connectionService.ts:36 🔍 [ConnectionService] Returning pending connection check
connectionService.ts:75 ✅ [ConnectionService] Database connection healthy
connectionService.ts:57 🔍 [ConnectionService] Checking database connection...
connectionService.ts:36 🔍 [ConnectionService] Returning pending connection check
connectionService.ts:36 🔍 [ConnectionService] Returning pending connection check
connectionService.ts:75 ✅ [ConnectionService] Database connection healthy
userService.ts:138 🔄 [UserService] Auth state changed: SIGNED_IN
userService.ts:126 🔄 [UserService] Clearing user cache and pending requests
userService.ts:147 👋 [UserService] User signed in - cache cleared for fresh data
userService.ts:138 🔄 [UserService] Auth state changed: SIGNED_IN
userService.ts:126 🔄 [UserService] Clearing user cache and pending requests
userService.ts:147 👋 [UserService] User signed in - cache cleared for fresh data
App.tsx:334 Auth state changed: SIGNED_IN <EMAIL>
dataService.ts:33 🔍 [DataService] Delegating to UserService.getCurrentUser
userService.ts:26 🔍 [UserService] getCurrentUser called
dataService.ts:257 🔍 [DataService] Delegating to ChatService.getChatSessions
connectionService.ts:151 🔍 [ConnectionService] Executing getChatSessions with healthy connection
chatService.ts:24 🔍 [ChatService] getChatSessions called
connectionService.ts:201 🔍 [ConnectionService] Checking auth session...
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
userService.ts:117 ❌ [UserService] fetchCurrentUser error: Error: Session check timeout
    at userService.ts:72:33
fetchCurrentUser @ userService.ts:117
await in fetchCurrentUser
getCurrentUser @ userService.ts:41
getCurrentUser @ dataService.ts:34
getUserProfiles @ dataService.ts:235
(anonymous) @ App.tsx:340
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:6695
_notifyAllSubscribers @ @supabase_supabase-js.js?v=ea90511a:6693
_recoverAndRefresh @ @supabase_supabase-js.js?v=ea90511a:6636
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:6908
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:5939
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:5056
connectionService.ts:245 ❌ [ConnectionService] performAuthSessionCheck error: Error: Auth session check timeout
    at connectionService.ts:206:33
performAuthSessionCheck @ connectionService.ts:245
await in performAuthSessionCheck
ensureAuthSession @ connectionService.ts:185
(anonymous) @ chatService.ts:27
executeWithRetry @ connectionService.ts:152
await in executeWithRetry
getChatSessions @ chatService.ts:23
getChatSessions @ dataService.ts:258
(anonymous) @ App.tsx:341
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:6695
_notifyAllSubscribers @ @supabase_supabase-js.js?v=ea90511a:6693
_recoverAndRefresh @ @supabase_supabase-js.js?v=ea90511a:6636
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:6908
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:5939
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:5056
chatService.ts:29 🔍 [ChatService] No valid authenticated session
App.tsx:348 🔍 [App] Auth change - Admin status: false User role: undefined
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: false, currentUser: {…}, userRole: undefined}
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
userService.ts:138 🔄 [UserService] Auth state changed: SIGNED_OUT
userService.ts:126 🔄 [UserService] Clearing user cache and pending requests
userService.ts:145 👋 [UserService] User signed out - cache cleared
userService.ts:138 🔄 [UserService] Auth state changed: SIGNED_OUT
userService.ts:126 🔄 [UserService] Clearing user cache and pending requests
userService.ts:145 👋 [UserService] User signed out - cache cleared
App.tsx:334 Auth state changed: SIGNED_OUT undefined
App.tsx:872 🔍 [App] Current state: {isAuthenticated: false, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: false, isAdmin: false, currentUser: {…}, userRole: undefined}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:872 🔍 [App] Current state: {isAuthenticated: false, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: false, isAdmin: false, currentUser: {…}, userRole: undefined}
connectionService.ts:57 🔍 [ConnectionService] Checking database connection...
connectionService.ts:36 🔍 [ConnectionService] Returning pending connection check
connectionService.ts:36 🔍 [ConnectionService] Returning pending connection check
connectionService.ts:75 ✅ [ConnectionService] Database connection healthy
App.tsx:872 🔍 [App] Current state: {isAuthenticated: false, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: false, isAdmin: false, currentUser: {…}, userRole: undefined}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:872 🔍 [App] Current state: {isAuthenticated: false, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: false, isAdmin: false, currentUser: {…}, userRole: undefined}
contentScript.bundle.js:31 Unsupported domain for continue button
userService.ts:138 🔄 [UserService] Auth state changed: SIGNED_IN
userService.ts:126 🔄 [UserService] Clearing user cache and pending requests
userService.ts:147 👋 [UserService] User signed in - cache cleared for fresh data
userService.ts:138 🔄 [UserService] Auth state changed: SIGNED_IN
userService.ts:126 🔄 [UserService] Clearing user cache and pending requests
userService.ts:147 👋 [UserService] User signed in - cache cleared for fresh data
App.tsx:334 Auth state changed: SIGNED_IN <EMAIL>
dataService.ts:33 🔍 [DataService] Delegating to UserService.getCurrentUser
userService.ts:26 🔍 [UserService] getCurrentUser called
dataService.ts:257 🔍 [DataService] Delegating to ChatService.getChatSessions
connectionService.ts:151 🔍 [ConnectionService] Executing getChatSessions with healthy connection
chatService.ts:24 🔍 [ChatService] getChatSessions called
connectionService.ts:201 🔍 [ConnectionService] Checking auth session...
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: false, currentUser: {…}, userRole: undefined}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: false, currentUser: {…}, userRole: undefined}
contentScript.bundle.js:31 Unsupported domain for continue button
userService.ts:83 🔍 [UserService] Fetching profile for user: fc025615-93c4-4d2e-ba33-c32ad7edb30c
connectionService.ts:242 ✅ [ConnectionService] Auth session is valid
chatService.ts:41 🔍 [ChatService] Fetching chat sessions for user: fc025615-93c4-4d2e-ba33-c32ad7edb30c
chatService.ts:54 🔍 [ChatService] Raw chat sessions: (2) [{…}, {…}]
chatService.ts:64 🔍 [ChatService] Converted chat sessions: (2) [{…}, {…}]
userService.ts:108 🔍 [UserService] Current user profile: {id: 'fc025615-93c4-4d2e-ba33-c32ad7edb30c', name: 'Sergey V. Ryzhkov', avatar_url: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD…7V4igK7eUqhwDt7MmZKKVd1lPiBIX6y0skEEY80P9xentR//Z', email: '<EMAIL>', created_at: '2025-07-14T07:18:15.062147+00:00', …}
App.tsx:348 🔍 [App] Auth change - Admin status: true User role: admin
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:467 🔍 [handleSelectChat] Chat selected: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:492 🔍 [handleSelectChat] Selected session: This is a new chat... Messages count: 0
App.tsx:496 🔍 [handleSelectChat] Loading messages for chat: bce18b79-966f-4868-8844-e67143528bb8
dataService.ts:268 🔍 [DataService] Delegating to ChatService.getMessagesForSession
contentScript.bundle.js:31 Unsupported domain for continue button
connectionService.ts:151 🔍 [ConnectionService] Executing getMessagesForSession(bce18b79-966f-4868-8844-e67143528bb8) with healthy connection
chatService.ts:76 🔍 [ChatService] getMessagesForSession called for: bce18b79-966f-4868-8844-e67143528bb8
connectionService.ts:201 🔍 [ConnectionService] Checking auth session...
connectionService.ts:242 ✅ [ConnectionService] Auth session is valid
chatService.ts:96 🔍 [ChatService] Raw messages: (11) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
chatService.ts:110 🔍 [ChatService] Converted messages: (11) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
App.tsx:521 ✅ [handleSelectChat] Messages loaded for chat: bce18b79-966f-4868-8844-e67143528bb8 Count: 11
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
connectionService.ts:57 🔍 [ConnectionService] Checking database connection...
connectionService.ts:36 🔍 [ConnectionService] Returning pending connection check
connectionService.ts:36 🔍 [ConnectionService] Returning pending connection check
contentScript.bundle.js:31 Unsupported domain for continue button
connectionService.ts:75 ✅ [ConnectionService] Database connection healthy
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
connectionService.ts:57 🔍 [ConnectionService] Checking database connection...
connectionService.ts:36 🔍 [ConnectionService] Returning pending connection check
connectionService.ts:36 🔍 [ConnectionService] Returning pending connection check
connectionService.ts:75 ✅ [ConnectionService] Database connection healthy
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: undefined, isNew: false, hasUnsavedMessages: true, shouldSave: true, messageCount: 12, …}
connectionService.ts:57 🔍 [ConnectionService] Checking database connection...
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: false messages: 12
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 12 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
MainArea.tsx:506 🚀 Starting AI streaming response...
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 0 Text preview: ...
chunk-TH7NCS4R.js?v=ea90511a:18675 [Violation] 'keydown' handler took 336ms
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:619 🔍 [updateChatSession] Authentication check: true
App.tsx:620 🔍 [updateChatSession] Chat ID: bce18b79-966f-4868-8844-e67143528bb8 Messages count: 12
App.tsx:673 🔍 [updateChatSession] Updating existing chat session: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:691 🔍 [updateChatSession] Will save message: user-1753090418776 user complete How to define whether the issu
App.tsx:697 🔍 [updateChatSession] Messages to save: 1
App.tsx:701 🔍 [updateChatSession] Filtered unsaved messages: 1 of 1
App.tsx:705 🔍 [updateChatSession] Saving new message to existing chat: user-1753090418776
dataService.ts:290 🔍 [DataService] Delegating to ChatService.addMessage
chatService.ts:172 🔍 [ChatService] addMessage called: {chatSessionId: 'bce18b79-966f-4868-8844-e67143528bb8', messageText: 'How to define whether the issue depends on connect', sender: 'user', isLoading: undefined}
connectionService.ts:36 🔍 [ConnectionService] Returning pending connection check
connectionService.ts:75 ✅ [ConnectionService] Database connection healthy
connectionService.ts:151 🔍 [ConnectionService] Executing addMessage with healthy connection
chatService.ts:200 🔍 [ChatService] Validating auth session for addMessage
connectionService.ts:201 🔍 [ConnectionService] Checking auth session...
connectionService.ts:242 ✅ [ConnectionService] Auth session is valid
chatService.ts:235 ✅ [ChatService] Auth validation passed, inserting message
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 4 Text preview: Okay...
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 14 Text preview: Okay, figuring...
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 113 Text preview: Okay, figuring out if an issue is caused by connec...
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
chatService.ts:248 ✅ [ChatService] Message added successfully: 8cbd6d7f-bb0f-4b57-89d6-e154fb221ac4
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 209 Text preview: Okay, figuring out if an issue is caused by connec...
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 359 Text preview: Okay, figuring out if an issue is caused by connec...
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 490 Text preview: Okay, figuring out if an issue is caused by connec...
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 703 Text preview: Okay, figuring out if an issue is caused by connec...
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
App.tsx:707 🔍 [updateChatSession] Message saved to existing chat: 8cbd6d7f-bb0f-4b57-89d6-e154fb221ac4
App.tsx:730 🔍 [updateChatSession] Found unsaved AI message: d71486d4-dedb-400e-9208-2469c8af81b0 Okay, I can definitely help you troubleshoot stora
App.tsx:730 🔍 [updateChatSession] Found unsaved AI message: 49eaa799-c160-4e29-bcd1-158377e544c7 Okay, I can definitely help you troubleshoot stora
App.tsx:730 🔍 [updateChatSession] Found unsaved AI message: 1fd608ed-24a8-4966-98e1-e5901a415f34 That's a good question! Deploying your app to web 
App.tsx:730 🔍 [updateChatSession] Found unsaved AI message: af146175-0f7d-42b1-a5ba-9f7bf8796bda Okay, I can definitely help you troubleshoot stora
App.tsx:730 🔍 [updateChatSession] Found unsaved AI message: 2029937c-3659-43c9-845f-0b96c43672f8 Okay, I can definitely help you troubleshoot stora
App.tsx:730 🔍 [updateChatSession] Found unsaved AI message: a12f1044-41f9-44ae-8fd5-fdceff5166f7 That's a good question! Deploying your app to web 
App.tsx:730 🔍 [updateChatSession] Found unsaved AI message: b40be93b-943b-4392-839e-9abf5969aad0 I understand your situation. Dealing with a slow a
App.tsx:739 🔍 [updateChatSession] Filtered unsaved completed messages: 7 of 7
App.tsx:743 🔍 [updateChatSession] Saving completed streaming message: d71486d4-dedb-400e-9208-2469c8af81b0
dataService.ts:290 🔍 [DataService] Delegating to ChatService.addMessage
chatService.ts:172 🔍 [ChatService] addMessage called: {chatSessionId: 'bce18b79-966f-4868-8844-e67143528bb8', messageText: 'Okay, I can definitely help you troubleshoot stora', sender: 'ai', isLoading: false}
connectionService.ts:151 🔍 [ConnectionService] Executing addMessage with healthy connection
chatService.ts:200 🔍 [ChatService] Validating auth session for addMessage
connectionService.ts:201 🔍 [ConnectionService] Checking auth session...
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 931 Text preview: Okay, figuring out if an issue is caused by connec...
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 1227 Text preview: Okay, figuring out if an issue is caused by connec...
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
connectionService.ts:242 ✅ [ConnectionService] Auth session is valid
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 1512 Text preview: Okay, figuring out if an issue is caused by connec...
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 1806 Text preview: Okay, figuring out if an issue is caused by connec...
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 2095 Text preview: Okay, figuring out if an issue is caused by connec...
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 2364 Text preview: Okay, figuring out if an issue is caused by connec...
chatService.ts:235 ✅ [ChatService] Auth validation passed, inserting message
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 2641 Text preview: Okay, figuring out if an issue is caused by connec...
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
chatService.ts:248 ✅ [ChatService] Message added successfully: 58cec672-0617-422e-89ba-3f39ea24d032
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 2886 Text preview: Okay, figuring out if an issue is caused by connec...
App.tsx:745 🔍 [updateChatSession] Completed streaming message saved: 58cec672-0617-422e-89ba-3f39ea24d032
App.tsx:750 🔍 [updateChatSession] Added message ID to savedMessageIds: d71486d4-dedb-400e-9208-2469c8af81b0
App.tsx:743 🔍 [updateChatSession] Saving completed streaming message: 49eaa799-c160-4e29-bcd1-158377e544c7
dataService.ts:290 🔍 [DataService] Delegating to ChatService.addMessage
chatService.ts:172 🔍 [ChatService] addMessage called: {chatSessionId: 'bce18b79-966f-4868-8844-e67143528bb8', messageText: 'Okay, I can definitely help you troubleshoot stora', sender: 'ai', isLoading: false}
connectionService.ts:151 🔍 [ConnectionService] Executing addMessage with healthy connection
chatService.ts:200 🔍 [ChatService] Validating auth session for addMessage
connectionService.ts:201 🔍 [ConnectionService] Checking auth session...
connectionService.ts:242 ✅ [ConnectionService] Auth session is valid
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 3170 Text preview: Okay, figuring out if an issue is caused by connec...
chatService.ts:235 ✅ [ChatService] Auth validation passed, inserting message
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 3455 Text preview: Okay, figuring out if an issue is caused by connec...
chatService.ts:248 ✅ [ChatService] Message added successfully: 3ed90e31-560d-4894-a806-e1522a8f42e6
App.tsx:745 🔍 [updateChatSession] Completed streaming message saved: 3ed90e31-560d-4894-a806-e1522a8f42e6
App.tsx:750 🔍 [updateChatSession] Added message ID to savedMessageIds: 49eaa799-c160-4e29-bcd1-158377e544c7
App.tsx:743 🔍 [updateChatSession] Saving completed streaming message: 1fd608ed-24a8-4966-98e1-e5901a415f34
dataService.ts:290 🔍 [DataService] Delegating to ChatService.addMessage
chatService.ts:172 🔍 [ChatService] addMessage called: {chatSessionId: 'bce18b79-966f-4868-8844-e67143528bb8', messageText: "That's a good question! Deploying your app to web ", sender: 'ai', isLoading: false}
connectionService.ts:151 🔍 [ConnectionService] Executing addMessage with healthy connection
chatService.ts:200 🔍 [ChatService] Validating auth session for addMessage
connectionService.ts:201 🔍 [ConnectionService] Checking auth session...
connectionService.ts:242 ✅ [ConnectionService] Auth session is valid
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 3750 Text preview: Okay, figuring out if an issue is caused by connec...
chatService.ts:235 ✅ [ChatService] Auth validation passed, inserting message
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 3968 Text preview: Okay, figuring out if an issue is caused by connec...
chatService.ts:248 ✅ [ChatService] Message added successfully: 4306af48-3249-4520-be8b-3150987312da
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
App.tsx:745 🔍 [updateChatSession] Completed streaming message saved: 4306af48-3249-4520-be8b-3150987312da
App.tsx:750 🔍 [updateChatSession] Added message ID to savedMessageIds: 1fd608ed-24a8-4966-98e1-e5901a415f34
App.tsx:743 🔍 [updateChatSession] Saving completed streaming message: af146175-0f7d-42b1-a5ba-9f7bf8796bda
dataService.ts:290 🔍 [DataService] Delegating to ChatService.addMessage
chatService.ts:172 🔍 [ChatService] addMessage called: {chatSessionId: 'bce18b79-966f-4868-8844-e67143528bb8', messageText: 'Okay, I can definitely help you troubleshoot stora', sender: 'ai', isLoading: false}
connectionService.ts:151 🔍 [ConnectionService] Executing addMessage with healthy connection
chatService.ts:200 🔍 [ChatService] Validating auth session for addMessage
connectionService.ts:201 🔍 [ConnectionService] Checking auth session...
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 4299 Text preview: Okay, figuring out if an issue is caused by connec...
connectionService.ts:242 ✅ [ConnectionService] Auth session is valid
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 4578 Text preview: Okay, figuring out if an issue is caused by connec...
chatService.ts:235 ✅ [ChatService] Auth validation passed, inserting message
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 4840 Text preview: Okay, figuring out if an issue is caused by connec...
chatService.ts:248 ✅ [ChatService] Message added successfully: 5963b827-7204-45f7-b051-25af1592d9fe
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 5103 Text preview: Okay, figuring out if an issue is caused by connec...
App.tsx:745 🔍 [updateChatSession] Completed streaming message saved: 5963b827-7204-45f7-b051-25af1592d9fe
App.tsx:750 🔍 [updateChatSession] Added message ID to savedMessageIds: af146175-0f7d-42b1-a5ba-9f7bf8796bda
App.tsx:743 🔍 [updateChatSession] Saving completed streaming message: 2029937c-3659-43c9-845f-0b96c43672f8
dataService.ts:290 🔍 [DataService] Delegating to ChatService.addMessage
chatService.ts:172 🔍 [ChatService] addMessage called: {chatSessionId: 'bce18b79-966f-4868-8844-e67143528bb8', messageText: 'Okay, I can definitely help you troubleshoot stora', sender: 'ai', isLoading: false}
connectionService.ts:151 🔍 [ConnectionService] Executing addMessage with healthy connection
chatService.ts:200 🔍 [ChatService] Validating auth session for addMessage
connectionService.ts:201 🔍 [ConnectionService] Checking auth session...
connectionService.ts:242 ✅ [ConnectionService] Auth session is valid
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 5363 Text preview: Okay, figuring out if an issue is caused by connec...
chatService.ts:235 ✅ [ChatService] Auth validation passed, inserting message
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 5640 Text preview: Okay, figuring out if an issue is caused by connec...
chatService.ts:248 ✅ [ChatService] Message added successfully: a4fac0c5-60db-4f3f-a358-95b778aab5da
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 5922 Text preview: Okay, figuring out if an issue is caused by connec...
App.tsx:745 🔍 [updateChatSession] Completed streaming message saved: a4fac0c5-60db-4f3f-a358-95b778aab5da
App.tsx:750 🔍 [updateChatSession] Added message ID to savedMessageIds: 2029937c-3659-43c9-845f-0b96c43672f8
App.tsx:743 🔍 [updateChatSession] Saving completed streaming message: a12f1044-41f9-44ae-8fd5-fdceff5166f7
dataService.ts:290 🔍 [DataService] Delegating to ChatService.addMessage
chatService.ts:172 🔍 [ChatService] addMessage called: {chatSessionId: 'bce18b79-966f-4868-8844-e67143528bb8', messageText: "That's a good question! Deploying your app to web ", sender: 'ai', isLoading: false}
connectionService.ts:151 🔍 [ConnectionService] Executing addMessage with healthy connection
chatService.ts:200 🔍 [ChatService] Validating auth session for addMessage
connectionService.ts:201 🔍 [ConnectionService] Checking auth session...
connectionService.ts:242 ✅ [ConnectionService] Auth session is valid
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 6206 Text preview: Okay, figuring out if an issue is caused by connec...
chatService.ts:235 ✅ [ChatService] Auth validation passed, inserting message
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 6471 Text preview: Okay, figuring out if an issue is caused by connec...
chatService.ts:248 ✅ [ChatService] Message added successfully: 7d94343c-708d-4018-a698-7fff7d8060f4
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 6751 Text preview: Okay, figuring out if an issue is caused by connec...
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
MainArea.tsx:558 🔍 [handleSendMessage] Stream completed, preparing final AI message for saving
MainArea.tsx:577 ✅ AI streaming response completed
MainArea.tsx:592 🔍 [handleSendMessage] Saving final AI response to database: ai-1753090418777
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: undefined, isNew: undefined, hasUnsavedMessages: true, shouldSave: true, messageCount: 13, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: bce18b79-966f-4868-8844-e67143528bb8 isNew: undefined messages: 13
App.tsx:745 🔍 [updateChatSession] Completed streaming message saved: 7d94343c-708d-4018-a698-7fff7d8060f4
App.tsx:750 🔍 [updateChatSession] Added message ID to savedMessageIds: a12f1044-41f9-44ae-8fd5-fdceff5166f7
App.tsx:743 🔍 [updateChatSession] Saving completed streaming message: b40be93b-943b-4392-839e-9abf5969aad0
dataService.ts:290 🔍 [DataService] Delegating to ChatService.addMessage
chatService.ts:172 🔍 [ChatService] addMessage called: {chatSessionId: 'bce18b79-966f-4868-8844-e67143528bb8', messageText: 'I understand your situation. Dealing with a slow a', sender: 'ai', isLoading: false}
connectionService.ts:151 🔍 [ConnectionService] Executing addMessage with healthy connection
chatService.ts:200 🔍 [ChatService] Validating auth session for addMessage
connectionService.ts:201 🔍 [ConnectionService] Checking auth session...
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:785 🔍 [updateChatSession] Updating existing session: bce18b79-966f-4868-8844-e67143528bb8 with 13 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:619 🔍 [updateChatSession] Authentication check: true
App.tsx:620 🔍 [updateChatSession] Chat ID: bce18b79-966f-4868-8844-e67143528bb8 Messages count: 13
App.tsx:673 🔍 [updateChatSession] Updating existing chat session: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:691 🔍 [updateChatSession] Will save message: ai-1753090418777 ai complete Okay, figuring out if an issue
App.tsx:697 🔍 [updateChatSession] Messages to save: 1
App.tsx:701 🔍 [updateChatSession] Filtered unsaved messages: 1 of 1
App.tsx:705 🔍 [updateChatSession] Saving new message to existing chat: ai-1753090418777
dataService.ts:290 🔍 [DataService] Delegating to ChatService.addMessage
chatService.ts:172 🔍 [ChatService] addMessage called: {chatSessionId: 'bce18b79-966f-4868-8844-e67143528bb8', messageText: 'Okay, figuring out if an issue is caused by connec', sender: 'ai', isLoading: false}
connectionService.ts:151 🔍 [ConnectionService] Executing addMessage with healthy connection
chatService.ts:200 🔍 [ChatService] Validating auth session for addMessage
connectionService.ts:180 🔍 [ConnectionService] Returning pending auth check
connectionService.ts:242 ✅ [ConnectionService] Auth session is valid
chatService.ts:235 ✅ [ChatService] Auth validation passed, inserting message
chatService.ts:248 ✅ [ChatService] Message added successfully: e1d21386-8019-4d8f-b73b-399b2fff7dad
chatService.ts:235 ✅ [ChatService] Auth validation passed, inserting message
App.tsx:745 🔍 [updateChatSession] Completed streaming message saved: e1d21386-8019-4d8f-b73b-399b2fff7dad
App.tsx:750 🔍 [updateChatSession] Added message ID to savedMessageIds: b40be93b-943b-4392-839e-9abf5969aad0
chatService.ts:248 ✅ [ChatService] Message added successfully: baff3ca0-7479-40ec-8c0b-393fe9e73776
App.tsx:707 🔍 [updateChatSession] Message saved to existing chat: baff3ca0-7479-40ec-8c0b-393fe9e73776
App.tsx:739 🔍 [updateChatSession] Filtered unsaved completed messages: 0 of 0
userService.ts:138 🔄 [UserService] Auth state changed: SIGNED_IN
userService.ts:126 🔄 [UserService] Clearing user cache and pending requests
userService.ts:147 👋 [UserService] User signed in - cache cleared for fresh data
userService.ts:138 🔄 [UserService] Auth state changed: SIGNED_IN
userService.ts:126 🔄 [UserService] Clearing user cache and pending requests
userService.ts:147 👋 [UserService] User signed in - cache cleared for fresh data
App.tsx:334 Auth state changed: SIGNED_IN <EMAIL>
dataService.ts:33 🔍 [DataService] Delegating to UserService.getCurrentUser
userService.ts:26 🔍 [UserService] getCurrentUser called
dataService.ts:257 🔍 [DataService] Delegating to ChatService.getChatSessions
connectionService.ts:57 🔍 [ConnectionService] Checking database connection...
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
connectionService.ts:36 🔍 [ConnectionService] Returning pending connection check
connectionService.ts:36 🔍 [ConnectionService] Returning pending connection check
userService.ts:117 ❌ [UserService] fetchCurrentUser error: Error: Session check timeout
    at userService.ts:72:33
fetchCurrentUser @ userService.ts:117
await in fetchCurrentUser
getCurrentUser @ userService.ts:41
getCurrentUser @ dataService.ts:34
getUserProfiles @ dataService.ts:235
(anonymous) @ App.tsx:340
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:6695
_notifyAllSubscribers @ @supabase_supabase-js.js?v=ea90511a:6693
_recoverAndRefresh @ @supabase_supabase-js.js?v=ea90511a:6636
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:6908
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:5939
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:5056
connectionService.ts:78 ❌ [ConnectionService] Database connection failed: Error: Connection timeout
    at connectionService.ts:66:33
performConnectionCheck @ connectionService.ts:78
await in performConnectionCheck
checkConnection @ connectionService.ts:41
ensureConnection @ connectionService.ts:94
executeWithRetry @ connectionService.ts:145
getChatSessions @ chatService.ts:23
getChatSessions @ dataService.ts:258
(anonymous) @ App.tsx:341
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:6695
_notifyAllSubscribers @ @supabase_supabase-js.js?v=ea90511a:6693
_recoverAndRefresh @ @supabase_supabase-js.js?v=ea90511a:6636
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:6908
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:5939
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:5056
connectionService.ts:97 🔄 [ConnectionService] Retrying connection...
connectionService.ts:57 🔍 [ConnectionService] Checking database connection...
connectionService.ts:78 ❌ [ConnectionService] Database connection failed: Error: Connection timeout
    at connectionService.ts:66:33
performConnectionCheck @ connectionService.ts:78
await in performConnectionCheck
checkConnection @ connectionService.ts:41
ensureConnection @ connectionService.ts:94
ensureConnection @ connectionService.ts:99
await in ensureConnection
executeWithRetry @ connectionService.ts:145
getChatSessions @ chatService.ts:23
getChatSessions @ dataService.ts:258
(anonymous) @ App.tsx:341
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:6695
_notifyAllSubscribers @ @supabase_supabase-js.js?v=ea90511a:6693
_recoverAndRefresh @ @supabase_supabase-js.js?v=ea90511a:6636
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:6908
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:5939
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:5056
connectionService.ts:97 🔄 [ConnectionService] Retrying connection...
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
connectionService.ts:57 🔍 [ConnectionService] Checking database connection...
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
connectionService.ts:36 🔍 [ConnectionService] Returning pending connection check
connectionService.ts:36 🔍 [ConnectionService] Returning pending connection check
connectionService.ts:36 🔍 [ConnectionService] Returning pending connection check
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
connectionService.ts:78 ❌ [ConnectionService] Database connection failed: Error: Connection timeout
    at connectionService.ts:66:33
performConnectionCheck @ connectionService.ts:78
await in performConnectionCheck
checkConnection @ connectionService.ts:41
ensureConnection @ connectionService.ts:94
ensureConnection @ connectionService.ts:99
await in ensureConnection
ensureConnection @ connectionService.ts:99
await in ensureConnection
executeWithRetry @ connectionService.ts:145
getChatSessions @ chatService.ts:23
getChatSessions @ dataService.ts:258
(anonymous) @ App.tsx:341
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:6695
_notifyAllSubscribers @ @supabase_supabase-js.js?v=ea90511a:6693
_recoverAndRefresh @ @supabase_supabase-js.js?v=ea90511a:6636
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:6908
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:5939
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:5056
connectionService.ts:147 ❌ [ConnectionService] Cannot execute getChatSessions - connection not ready
executeWithRetry @ connectionService.ts:147
await in executeWithRetry
getChatSessions @ chatService.ts:23
getChatSessions @ dataService.ts:258
(anonymous) @ App.tsx:341
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:6695
_notifyAllSubscribers @ @supabase_supabase-js.js?v=ea90511a:6693
_recoverAndRefresh @ @supabase_supabase-js.js?v=ea90511a:6636
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:6908
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:5939
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:5056
App.tsx:348 🔍 [App] Auth change - Admin status: false User role: undefined
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: false, currentUser: {…}, userRole: undefined}
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
connectionService.ts:57 🔍 [ConnectionService] Checking database connection...
connectionService.ts:36 🔍 [ConnectionService] Returning pending connection check
connectionService.ts:75 ✅ [ConnectionService] Database connection healthy
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
userService.ts:138 🔄 [UserService] Auth state changed: SIGNED_OUT
userService.ts:126 🔄 [UserService] Clearing user cache and pending requests
userService.ts:145 👋 [UserService] User signed out - cache cleared
userService.ts:138 🔄 [UserService] Auth state changed: SIGNED_OUT
userService.ts:126 🔄 [UserService] Clearing user cache and pending requests
userService.ts:145 👋 [UserService] User signed out - cache cleared
App.tsx:334 Auth state changed: SIGNED_OUT undefined
App.tsx:872 🔍 [App] Current state: {isAuthenticated: false, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: false, isAdmin: false, currentUser: {…}, userRole: undefined}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:872 🔍 [App] Current state: {isAuthenticated: false, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: false, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: false, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: false, isAdmin: false, currentUser: {…}, userRole: undefined}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:872 🔍 [App] Current state: {isAuthenticated: false, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: false, isAdmin: false, currentUser: {…}, userRole: undefined}
contentScript.bundle.js:31 Unsupported domain for continue button
userService.ts:138 🔄 [UserService] Auth state changed: SIGNED_IN
userService.ts:126 🔄 [UserService] Clearing user cache and pending requests
userService.ts:147 👋 [UserService] User signed in - cache cleared for fresh data
userService.ts:138 🔄 [UserService] Auth state changed: SIGNED_IN
userService.ts:126 🔄 [UserService] Clearing user cache and pending requests
userService.ts:147 👋 [UserService] User signed in - cache cleared for fresh data
App.tsx:334 Auth state changed: SIGNED_IN <EMAIL>
dataService.ts:33 🔍 [DataService] Delegating to UserService.getCurrentUser
userService.ts:26 🔍 [UserService] getCurrentUser called
dataService.ts:257 🔍 [DataService] Delegating to ChatService.getChatSessions
connectionService.ts:151 🔍 [ConnectionService] Executing getChatSessions with healthy connection
chatService.ts:24 🔍 [ChatService] getChatSessions called
connectionService.ts:201 🔍 [ConnectionService] Checking auth session...
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: false, currentUser: {…}, userRole: undefined}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: false, currentUser: {…}, userRole: undefined}
contentScript.bundle.js:31 Unsupported domain for continue button
userService.ts:83 🔍 [UserService] Fetching profile for user: fc025615-93c4-4d2e-ba33-c32ad7edb30c
connectionService.ts:242 ✅ [ConnectionService] Auth session is valid
chatService.ts:41 🔍 [ChatService] Fetching chat sessions for user: fc025615-93c4-4d2e-ba33-c32ad7edb30c
chatService.ts:54 🔍 [ChatService] Raw chat sessions: (2) [{…}, {…}]
chatService.ts:64 🔍 [ChatService] Converted chat sessions: (2) [{…}, {…}]
userService.ts:108 🔍 [UserService] Current user profile: {id: 'fc025615-93c4-4d2e-ba33-c32ad7edb30c', name: 'Sergey V. Ryzhkov', avatar_url: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD…7V4igK7eUqhwDt7MmZKKVd1lPiBIX6y0skEEY80P9xentR//Z', email: '<EMAIL>', created_at: '2025-07-14T07:18:15.062147+00:00', …}
App.tsx:348 🔍 [App] Auth change - Admin status: true User role: admin
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: undefined, isNew: true, hasUnsavedMessages: true, shouldSave: true, messageCount: 1, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: temp-1753090542881 isNew: true messages: 1
MainArea.tsx:506 🚀 Starting AI streaming response...
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 2, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: temp-1753090542881 isNew: undefined messages: 2
App.tsx:785 🔍 [updateChatSession] Updating existing session: temp-1753090542881 with 2 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: temp-1753090542881 with 2 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 0 Text preview: ...
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:619 🔍 [updateChatSession] Authentication check: true
App.tsx:620 🔍 [updateChatSession] Chat ID: temp-1753090542881 Messages count: 1
App.tsx:625 🔍 [updateChatSession] Creating new chat session...
dataService.ts:279 🔍 [DataService] Delegating to ChatService.createChatSession
connectionService.ts:151 🔍 [ConnectionService] Executing createChatSession with healthy connection
chatService.ts:122 🔍 [ChatService] createChatSession called: {title: 'I want to check the...', iconName: 'SpeechBubbleIcon'}
connectionService.ts:201 🔍 [ConnectionService] Checking auth session...
connectionService.ts:242 ✅ [ConnectionService] Auth session is valid
chatService.ts:156 🔍 [ChatService] Chat session created: {id: '878b2ead-aea6-4075-9ab6-1d61997250b5', user_id: 'fc025615-93c4-4d2e-ba33-c32ad7edb30c', title: 'I want to check the...', icon_name: 'SpeechBubbleIcon', created_at: '2025-07-21T09:35:42.923+00:00', …}
App.tsx:631 🔍 [updateChatSession] New session created: {id: '878b2ead-aea6-4075-9ab6-1d61997250b5', title: 'I want to check the...', messages: Array(0), lastActivity: Mon Jul 21 2025 12:35:42 GMT+0300 (Moscow Standard Time), iconName: 'SpeechBubbleIcon'}
App.tsx:639 🔍 [updateChatSession] Saving messages to Supabase: 1 (excluding streaming and already saved)
App.tsx:643 🔍 [updateChatSession] Saving message: user-1753090542881 I want to check the connection stability?
dataService.ts:290 🔍 [DataService] Delegating to ChatService.addMessage
chatService.ts:172 🔍 [ChatService] addMessage called: {chatSessionId: '878b2ead-aea6-4075-9ab6-1d61997250b5', messageText: 'I want to check the connection stability?', sender: 'user', isLoading: undefined}
connectionService.ts:151 🔍 [ConnectionService] Executing addMessage with healthy connection
chatService.ts:200 🔍 [ChatService] Validating auth session for addMessage
connectionService.ts:201 🔍 [ConnectionService] Checking auth session...
connectionService.ts:242 ✅ [ConnectionService] Auth session is valid
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 2, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: temp-1753090542881 isNew: undefined messages: 2
App.tsx:785 🔍 [updateChatSession] Updating existing session: temp-1753090542881 with 2 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 2, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: temp-1753090542881 isNew: undefined messages: 2
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 4 Text preview: Okay...
App.tsx:785 🔍 [updateChatSession] Updating existing session: temp-1753090542881 with 2 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: temp-1753090542881 with 2 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 14 Text preview: Okay! To check...
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 2, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: temp-1753090542881 isNew: undefined messages: 2
App.tsx:785 🔍 [updateChatSession] Updating existing session: temp-1753090542881 with 2 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: temp-1753090542881 with 2 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 109 Text preview: Okay! To check your connection stability, I need a...
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 2, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: temp-1753090542881 isNew: undefined messages: 2
App.tsx:785 🔍 [updateChatSession] Updating existing session: temp-1753090542881 with 2 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: temp-1753090542881 with 2 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 169 Text preview: Okay! To check your connection stability, I need a...
chatService.ts:235 ✅ [ChatService] Auth validation passed, inserting message
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 2, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: temp-1753090542881 isNew: undefined messages: 2
App.tsx:785 🔍 [updateChatSession] Updating existing session: temp-1753090542881 with 2 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: temp-1753090542881 with 2 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 259 Text preview: Okay! To check your connection stability, I need a...
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 2, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: temp-1753090542881 isNew: undefined messages: 2
chatService.ts:248 ✅ [ChatService] Message added successfully: 2db19993-e008-468a-b8e8-e26880da2e97
App.tsx:785 🔍 [updateChatSession] Updating existing session: temp-1753090542881 with 2 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: temp-1753090542881 with 2 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 367 Text preview: Okay! To check your connection stability, I need a...
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: true, shouldSave: false, messageCount: 2, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: temp-1753090542881 isNew: undefined messages: 2
App.tsx:785 🔍 [updateChatSession] Updating existing session: temp-1753090542881 with 2 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:785 🔍 [updateChatSession] Updating existing session: temp-1753090542881 with 2 messages
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
MainArea.tsx:378 🔄 [MainArea] Streaming message text length: 576 Text preview: Okay! To check your connection stability, I need a...
App.tsx:648 🔍 [updateChatSession] Message saved successfully: 2db19993-e008-468a-b8e8-e26880da2e97
App.tsx:663 🔍 [updateChatSession] Updated local state with Supabase chat ID: 878b2ead-aea6-4075-9ab6-1d61997250b5
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:663 🔍 [updateChatSession] Updated local state with Supabase chat ID: 878b2ead-aea6-4075-9ab6-1d61997250b5
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: false, shouldSave: false, messageCount: 2, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: temp-1753090542881 isNew: undefined messages: 2
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: false, shouldSave: false, messageCount: 2, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: temp-1753090542881 isNew: undefined messages: 2
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: false, shouldSave: false, messageCount: 2, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: temp-1753090542881 isNew: undefined messages: 2
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: false, shouldSave: false, messageCount: 2, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: temp-1753090542881 isNew: undefined messages: 2
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: false, shouldSave: false, messageCount: 2, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: temp-1753090542881 isNew: undefined messages: 2
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: false, shouldSave: false, messageCount: 2, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: temp-1753090542881 isNew: undefined messages: 2
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: false, shouldSave: false, messageCount: 2, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: temp-1753090542881 isNew: undefined messages: 2
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: false, shouldSave: false, messageCount: 2, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: temp-1753090542881 isNew: undefined messages: 2
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: false, shouldSave: false, messageCount: 2, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: temp-1753090542881 isNew: undefined messages: 2
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: true, isNew: undefined, hasUnsavedMessages: false, shouldSave: false, messageCount: 2, …}
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: temp-1753090542881 isNew: undefined messages: 2
MainArea.tsx:558 🔍 [handleSendMessage] Stream completed, preparing final AI message for saving
MainArea.tsx:577 ✅ AI streaming response completed
MainArea.tsx:592 🔍 [handleSendMessage] Saving final AI response to database: ai-1753090542882
App.tsx:594 🔍 [updateChatSession] Save decision: {skipSave: undefined, isNew: undefined, hasUnsavedMessages: true, shouldSave: true, messageCount: 2, …}
connectionService.ts:57 🔍 [ConnectionService] Checking database connection...
App.tsx:770 🔍 [updateChatSession] Updating local state for chat: temp-1753090542881 isNew: undefined messages: 2
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:792 🔍 [updateChatSession] Local state updated for chat: temp-1753090542881
App.tsx:872 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:619 🔍 [updateChatSession] Authentication check: true
App.tsx:620 🔍 [updateChatSession] Chat ID: temp-1753090542881 Messages count: 2
App.tsx:757 🔍 [updateChatSession] Skipping database save for temporary chat ID: temp-1753090542881 but will update local state
connectionService.ts:75 ✅ [ConnectionService] Database connection healthy
