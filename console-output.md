(index):24 [EARLY] window.location.href: http://localhost:5173/
(index):25 [EARLY] window.location.search: 
(index):26 [EARLY] window.location.hash: 
(index):64 cdn.tailwindcss.com should not be used in production. To use Tailwind CSS in production, install it as a PostCSS plugin or use the Tailwind CLI: https://tailwindcss.com/docs/installation
(anonymous) @ (index):64
(anonymous) @ (index):64
client:789 [vite] connecting...
client:912 [vite] connected.
chunk-TH7NCS4R.js?v=ea90511a:21609 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
contentScript.bundle.js:31 getIdsFromUrl result - promptId: null jobId: null
contentScript.bundle.js:31 Checking domain: localhost
contentScript.bundle.js:31 🔍 Delay detection: URL parameter 'null', using 1000ms delay
contentScript.bundle.js:31 🚀 Queue initialized with delay feature active (1000ms between prompts)
contentScript.bundle.js:31 ⚠️ IMPORTANT: Make sure URL has delay parameter if you want to customize delay time
contentScript.bundle.js:31 ✅ Queue initialization complete - ready to process messages
i18n.ts:32 i18next: languageChanged en
i18n.ts:32 i18next: initialized {debug: true, initImmediate: true, ns: Array(1), defaultNS: Array(1), fallbackLng: Array(1), …}
App.tsx:866 🔍 [App] Current state: {isAuthenticated: false, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:866 🔍 [App] Current state: {isAuthenticated: false, isAdmin: false, currentUser: {…}, userRole: undefined}
contentScript.bundle.js:31 Unsupported domain for continue button
App.tsx:220 🔍 [App] Starting connection monitoring
connectionService.ts:106 🔍 [ConnectionService] Starting health monitoring
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
App.tsx:238 🔍 [App] Starting initial data fetch...
App.tsx:244 🔍 [App] Testing Supabase connection...
react-router-dom.js?v=ea90511a:4393 ⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition.
warnOnce @ react-router-dom.js?v=ea90511a:4393
logDeprecation @ react-router-dom.js?v=ea90511a:4396
logV6DeprecationWarnings @ react-router-dom.js?v=ea90511a:4399
(anonymous) @ react-router-dom.js?v=ea90511a:5271
commitHookEffectListMount @ chunk-TH7NCS4R.js?v=ea90511a:16963
commitPassiveMountOnFiber @ chunk-TH7NCS4R.js?v=ea90511a:18211
commitPassiveMountEffects_complete @ chunk-TH7NCS4R.js?v=ea90511a:18179
commitPassiveMountEffects_begin @ chunk-TH7NCS4R.js?v=ea90511a:18169
commitPassiveMountEffects @ chunk-TH7NCS4R.js?v=ea90511a:18159
flushPassiveEffectsImpl @ chunk-TH7NCS4R.js?v=ea90511a:19543
flushPassiveEffects @ chunk-TH7NCS4R.js?v=ea90511a:19500
(anonymous) @ chunk-TH7NCS4R.js?v=ea90511a:19381
workLoop @ chunk-TH7NCS4R.js?v=ea90511a:197
flushWork @ chunk-TH7NCS4R.js?v=ea90511a:176
performWorkUntilDeadline @ chunk-TH7NCS4R.js?v=ea90511a:384
react-router-dom.js?v=ea90511a:4393 ⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath.
warnOnce @ react-router-dom.js?v=ea90511a:4393
logDeprecation @ react-router-dom.js?v=ea90511a:4396
logV6DeprecationWarnings @ react-router-dom.js?v=ea90511a:4402
(anonymous) @ react-router-dom.js?v=ea90511a:5271
commitHookEffectListMount @ chunk-TH7NCS4R.js?v=ea90511a:16963
commitPassiveMountOnFiber @ chunk-TH7NCS4R.js?v=ea90511a:18211
commitPassiveMountEffects_complete @ chunk-TH7NCS4R.js?v=ea90511a:18179
commitPassiveMountEffects_begin @ chunk-TH7NCS4R.js?v=ea90511a:18169
commitPassiveMountEffects @ chunk-TH7NCS4R.js?v=ea90511a:18159
flushPassiveEffectsImpl @ chunk-TH7NCS4R.js?v=ea90511a:19543
flushPassiveEffects @ chunk-TH7NCS4R.js?v=ea90511a:19500
(anonymous) @ chunk-TH7NCS4R.js?v=ea90511a:19381
workLoop @ chunk-TH7NCS4R.js?v=ea90511a:197
flushWork @ chunk-TH7NCS4R.js?v=ea90511a:176
performWorkUntilDeadline @ chunk-TH7NCS4R.js?v=ea90511a:384
App.tsx:220 🔍 [App] Starting connection monitoring
connectionService.ts:106 🔍 [ConnectionService] Starting health monitoring
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
App.tsx:238 🔍 [App] Starting initial data fetch...
App.tsx:244 🔍 [App] Testing Supabase connection...
App.tsx:866 🔍 [App] Current state: {isAuthenticated: false, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:866 🔍 [App] Current state: {isAuthenticated: false, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:328 Auth state changed: INITIAL_SESSION <EMAIL>
dataService.ts:33 🔍 [DataService] Delegating to UserService.getCurrentUser
userService.ts:23 🔍 [UserService] getCurrentUser called
dataService.ts:257 🔍 [DataService] Delegating to ChatService.getChatSessions
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
userService.ts:39 🔍 [UserService] Fetching profile for user: fc025615-93c4-4d2e-ba33-c32ad7edb30c
App.tsx:866 🔍 [App] Current state: {isAuthenticated: true, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:866 🔍 [App] Current state: {isAuthenticated: true, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:866 🔍 [App] Current state: {isAuthenticated: true, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:866 🔍 [App] Current state: {isAuthenticated: true, isAdmin: false, currentUser: {…}, userRole: undefined}
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
App.tsx:225 🔍 [App] Initial connection check: Connected
userService.ts:64 🔍 [UserService] Current user profile: {id: 'fc025615-93c4-4d2e-ba33-c32ad7edb30c', name: 'Sergey V. Ryzhkov', avatar_url: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD…7V4igK7eUqhwDt7MmZKKVd1lPiBIX6y0skEEY80P9xentR//Z', email: '<EMAIL>', created_at: '2025-07-14T07:18:15.062147+00:00', …}
App.tsx:252 ✅ [App] Supabase connection test successful
App.tsx:269 🔍 [App] Checking authentication status...
App.tsx:273 🔍 [App] Authentication status: true
App.tsx:283 🔍 [App] Fetching user profiles...
dataService.ts:33 🔍 [DataService] Delegating to UserService.getCurrentUser
userService.ts:23 🔍 [UserService] getCurrentUser called
App.tsx:866 🔍 [App] Current state: {isAuthenticated: true, isAdmin: false, currentUser: {…}, userRole: undefined}
App.tsx:866 🔍 [App] Current state: {isAuthenticated: true, isAdmin: false, currentUser: {…}, userRole: undefined}
userService.ts:39 🔍 [UserService] Fetching profile for user: fc025615-93c4-4d2e-ba33-c32ad7edb30c
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
App.tsx:225 🔍 [App] Initial connection check: Connected
App.tsx:252 ✅ [App] Supabase connection test successful
App.tsx:269 🔍 [App] Checking authentication status...
App.tsx:273 🔍 [App] Authentication status: true
App.tsx:283 🔍 [App] Fetching user profiles...
dataService.ts:33 🔍 [DataService] Delegating to UserService.getCurrentUser
userService.ts:23 🔍 [UserService] getCurrentUser called
userService.ts:39 🔍 [UserService] Fetching profile for user: fc025615-93c4-4d2e-ba33-c32ad7edb30c
userService.ts:64 🔍 [UserService] Current user profile: {id: 'fc025615-93c4-4d2e-ba33-c32ad7edb30c', name: 'Sergey V. Ryzhkov', avatar_url: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD…7V4igK7eUqhwDt7MmZKKVd1lPiBIX6y0skEEY80P9xentR//Z', email: '<EMAIL>', created_at: '2025-07-14T07:18:15.062147+00:00', …}
App.tsx:285 🔍 [App] User profiles data: true
App.tsx:294 🔍 [App] Fetching chat sessions...
dataService.ts:257 🔍 [DataService] Delegating to ChatService.getChatSessions
connectionService.ts:125 🔍 [ConnectionService] Executing getChatSessions with healthy connection
chatService.ts:24 🔍 [ChatService] getChatSessions called
chatService.ts:41 🔍 [ChatService] Fetching chat sessions for user: fc025615-93c4-4d2e-ba33-c32ad7edb30c
userService.ts:64 🔍 [UserService] Current user profile: {id: 'fc025615-93c4-4d2e-ba33-c32ad7edb30c', name: 'Sergey V. Ryzhkov', avatar_url: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD…7V4igK7eUqhwDt7MmZKKVd1lPiBIX6y0skEEY80P9xentR//Z', email: '<EMAIL>', created_at: '2025-07-14T07:18:15.062147+00:00', …}
App.tsx:285 🔍 [App] User profiles data: true
App.tsx:294 🔍 [App] Fetching chat sessions...
dataService.ts:257 🔍 [DataService] Delegating to ChatService.getChatSessions
connectionService.ts:125 🔍 [ConnectionService] Executing getChatSessions with healthy connection
chatService.ts:24 🔍 [ChatService] getChatSessions called
chatService.ts:41 🔍 [ChatService] Fetching chat sessions for user: fc025615-93c4-4d2e-ba33-c32ad7edb30c
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
connectionService.ts:125 🔍 [ConnectionService] Executing getChatSessions with healthy connection
chatService.ts:24 🔍 [ChatService] getChatSessions called
chatService.ts:54 🔍 [ChatService] Raw chat sessions: (2) [{…}, {…}]
chatService.ts:64 🔍 [ChatService] Converted chat sessions: (2) [{…}, {…}]
App.tsx:296 🔍 [App] Chat sessions data: 2
App.tsx:306 🔍 [App] Admin status: true User role: admin
App.tsx:310 ✅ [App] Initial data fetch completed successfully
App.tsx:319 🔍 [App] Setting dataLoading to false
App.tsx:866 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:866 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
contentScript.bundle.js:31 Unsupported domain for continue button
chatService.ts:41 🔍 [ChatService] Fetching chat sessions for user: fc025615-93c4-4d2e-ba33-c32ad7edb30c
chatService.ts:54 🔍 [ChatService] Raw chat sessions: (2) [{…}, {…}]
chatService.ts:64 🔍 [ChatService] Converted chat sessions: (2) [{…}, {…}]
App.tsx:296 🔍 [App] Chat sessions data: 2
App.tsx:306 🔍 [App] Admin status: true User role: admin
App.tsx:310 ✅ [App] Initial data fetch completed successfully
App.tsx:319 🔍 [App] Setting dataLoading to false
App.tsx:866 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:866 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
chatService.ts:54 🔍 [ChatService] Raw chat sessions: (2) [{…}, {…}]
chatService.ts:64 🔍 [ChatService] Converted chat sessions: (2) [{…}, {…}]
App.tsx:342 🔍 [App] Auth change - Admin status: true User role: admin
App.tsx:866 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:866 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
connectionService.ts:33 🔍 [ConnectionService] Checking database connection...
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
connectionService.ts:51 ✅ [ConnectionService] Database connection healthy
App.tsx:328 Auth state changed: SIGNED_IN <EMAIL>
dataService.ts:33 🔍 [DataService] Delegating to UserService.getCurrentUser
userService.ts:23 🔍 [UserService] getCurrentUser called
dataService.ts:257 🔍 [DataService] Delegating to ChatService.getChatSessions
connectionService.ts:125 🔍 [ConnectionService] Executing getChatSessions with healthy connection
chatService.ts:24 🔍 [ChatService] getChatSessions called
App.tsx:866 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:866 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:461 🔍 [handleSelectChat] Chat selected: bce18b79-966f-4868-8844-e67143528bb8
App.tsx:866 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:866 🔍 [App] Current state: {isAuthenticated: true, isAdmin: true, currentUser: {…}, userRole: 'admin'}
App.tsx:486 🔍 [handleSelectChat] Selected session: This is a new chat... Messages count: 0
App.tsx:490 🔍 [handleSelectChat] Loading messages for chat: bce18b79-966f-4868-8844-e67143528bb8
dataService.ts:268 🔍 [DataService] Delegating to ChatService.getMessagesForSession
contentScript.bundle.js:31 Unsupported domain for continue button
connectionService.ts:125 🔍 [ConnectionService] Executing getMessagesForSession(bce18b79-966f-4868-8844-e67143528bb8) with healthy connection
chatService.ts:76 🔍 [ChatService] getMessagesForSession called for: bce18b79-966f-4868-8844-e67143528bb8
userService.ts:73 ❌ [UserService] getCurrentUser error: Error: Session check timeout
    at userService.ts:28:33
getCurrentUser @ userService.ts:73
await in getCurrentUser
getCurrentUser @ dataService.ts:34
getUserProfiles @ dataService.ts:235
(anonymous) @ App.tsx:334
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:6695
_notifyAllSubscribers @ @supabase_supabase-js.js?v=ea90511a:6693
_recoverAndRefresh @ @supabase_supabase-js.js?v=ea90511a:6636
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:6908
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:5939
(anonymous) @ @supabase_supabase-js.js?v=ea90511a:5056
