# Authentication & Database Connection Fix

## Problem Summary
The application is experiencing authentication session timeouts and excessive concurrent database connection checks. The console shows:
- `userService.ts:73 ❌ [UserService] getCurrentUser error: Error: Session check timeout`
- Multiple repeated connection checks happening simultaneously
- Session validation failures during auth state transitions

## Required Fixes

### 1. Fix Session Timeout Issues
- **File**: `frontend/services/userService.ts`
- **Issue**: 5-second timeout is too short for auth operations
- **Fix**: Increase timeout to 10+ seconds and add proper session refresh logic

### 2. Implement Auth Session Caching
- **File**: `frontend/services/connectionService.ts` 
- **Issue**: Concurrent auth checks causing timeouts
- **Fix**: Add session caching with debouncing to prevent multiple simultaneous auth checks

### 3. Add Session Refresh Logic
- **File**: `frontend/services/connectionService.ts`
- **Issue**: No handling of expired tokens
- **Fix**: Implement automatic session refresh when tokens are expired

### 4. Reduce Concurrent Connection Checks
- **File**: `frontend/services/connectionService.ts`
- **Issue**: Multiple connection checks running simultaneously
- **Fix**: Implement singleton pattern or request deduplication

### 5. Improve Error Handling in Message Saving
- **File**: `frontend/services/chatService.ts`
- **Issue**: No proper auth validation before database operations
- **Fix**: Add comprehensive auth checks before saving messages

## Implementation Requirements

1. **Increase auth timeout** from 5s to 10s minimum
2. **Add session caching** with 30-second cache duration
3. **Implement session refresh** for expired tokens
4. **Add request deduplication** for connection checks
5. **Add proper logging** for debugging auth flow
6. **Test auth state transitions** thoroughly

## Success Criteria
- No more "Session check timeout" errors
- Reduced number of concurrent connection checks in console
- Successful message saving even during auth state changes
- Proper handling of expired sessions with automatic refresh

## Files to Modify
- `frontend/services/userService.ts`
- `frontend/services/connectionService.ts` 
- `frontend/services/chatService.ts`

## Testing Instructions
1. Test login/logout transitions
2. Test message saving during auth state changes
3. Verify no excessive connection checking in console
4. Test session expiration and refresh scenarios