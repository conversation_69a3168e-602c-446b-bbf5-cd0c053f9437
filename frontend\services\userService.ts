import { supabase } from '../lib/supabase';
import { User } from '../types';

/**
 * Dedicated User Service - Isolated from other features
 * This service handles ONLY user management operations
 */
export class UserService {
  private static instance: UserService;
  private userCache: { user: User | null; timestamp: number } | null = null;
  private readonly CACHE_DURATION = 30000; // 30 seconds
  private pendingUserRequest: Promise<User | null> | null = null;

  static getInstance(): UserService {
    if (!UserService.instance) {
      UserService.instance = new UserService();
    }
    return UserService.instance;
  }

  /**
   * Get current authenticated user with caching and deduplication
   */
  async getCurrentUser(): Promise<User | null> {
    try {
      console.log('🔍 [UserService] getCurrentUser called');

      // Check cache first
      if (this.userCache && (Date.now() - this.userCache.timestamp) < this.CACHE_DURATION) {
        console.log('🔍 [UserService] Returning cached user');
        return this.userCache.user;
      }

      // If there's already a pending request, return it to prevent concurrent calls
      if (this.pendingUserRequest) {
        console.log('🔍 [UserService] Returning pending user request');
        return this.pendingUserRequest;
      }

      // Create new request and cache it
      this.pendingUserRequest = this.fetchCurrentUser();

      try {
        const user = await this.pendingUserRequest;

        // Cache the result
        this.userCache = {
          user,
          timestamp: Date.now()
        };

        return user;
      } finally {
        // Clear pending request
        this.pendingUserRequest = null;
      }
    } catch (error) {
      console.error('❌ [UserService] getCurrentUser error:', error);
      this.pendingUserRequest = null;
      return null;
    }
  }

  /**
   * Internal method to fetch current user from database
   */
  private async fetchCurrentUser(): Promise<User | null> {
    try {
      // Add timeout to auth session check (increased from 5s to 10s)
      const sessionPromise = supabase.auth.getSession();
      const sessionTimeout = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Session check timeout')), 10000)
      );

      const { data: { session } } = await Promise.race([sessionPromise, sessionTimeout]) as any;
      const user = session?.user;

      if (!user) {
        console.log('🔍 [UserService] No authenticated user');
        return null;
      }

      console.log('🔍 [UserService] Fetching profile for user:', user.id);

      // Add timeout to profile fetch
      const profilePromise = supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      const profileTimeout = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Profile fetch timeout')), 10000)
      );

      const { data: profile, error } = await Promise.race([profilePromise, profileTimeout]) as any;

      if (error) {
        console.error('❌ [UserService] Error fetching profile:', error);
        return null;
      }

      if (!profile) {
        console.error('❌ [UserService] No profile found for user');
        return null;
      }

      console.log('🔍 [UserService] Current user profile:', profile);

      return {
        name: profile.name || user.email || '',
        email: profile.email || user.email || '',
        avatarUrl: profile.avatar_url || '',
        role: profile.role || 'user'
      };
    } catch (error) {
      console.error('❌ [UserService] fetchCurrentUser error:', error);
      return null;
    }
  }

  /**
   * Clear user cache (useful on logout or auth state changes)
   */
  clearUserCache(): void {
    console.log('🔄 [UserService] Clearing user cache and pending requests');
    this.userCache = null;
    this.pendingUserRequest = null;
  }

  /**
   * Initialize auth state monitoring for cache management
   */
  initializeAuthStateMonitoring(): void {
    console.log('🔍 [UserService] Initializing auth state monitoring');

    supabase.auth.onAuthStateChange((event, session) => {
      console.log('🔄 [UserService] Auth state changed:', event);

      // Clear cache on any auth state change
      this.clearUserCache();

      // Additional logging for debugging
      if (event === 'SIGNED_OUT') {
        console.log('👋 [UserService] User signed out - cache cleared');
      } else if (event === 'SIGNED_IN') {
        console.log('👋 [UserService] User signed in - cache cleared for fresh data');
      } else if (event === 'TOKEN_REFRESHED') {
        console.log('🔄 [UserService] Token refreshed - cache cleared');
      }
    });
  }

  /**
   * Check if current user is admin
   */
  async isUserAdmin(): Promise<boolean> {
    try {
      const user = await this.getCurrentUser();
      return user?.role === 'admin';
    } catch (error) {
      console.error('❌ [UserService] isUserAdmin error:', error);
      return false;
    }
  }

  /**
   * Get all users (admin only)
   */
  async getAllUsers() {
    try {
      console.log('🔍 [UserService] getAllUsers called');
      
      // Check if user is admin
      const isAdmin = await this.isUserAdmin();
      if (!isAdmin) {
        console.error('❌ [UserService] Access denied - not admin');
        return [];
      }

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('❌ [UserService] Error fetching users:', error);
        return [];
      }

      return data.map(user => ({
        id: user.id,
        name: user.name || '',
        email: user.email || '',
        role: user.role || 'user',
        avatarUrl: user.avatar_url || '',
        createdAt: new Date(user.created_at),
        lastActive: user.last_active ? new Date(user.last_active) : new Date(user.created_at)
      }));
    } catch (error) {
      console.error('❌ [UserService] getAllUsers error:', error);
      return [];
    }
  }

  /**
   * Update user (admin only)
   */
  async updateUser(userId: string, updates: { name?: string, email?: string, role?: 'admin' | 'user', avatarUrl?: string }) {
    try {
      console.log('🔍 [UserService] updateUser called:', { userId, updates });
      
      // Check if user is admin
      const isAdmin = await this.isUserAdmin();
      if (!isAdmin) {
        console.error('❌ [UserService] Access denied - not admin');
        return null;
      }

      const profileUpdates: any = {};
      if (updates.name !== undefined) profileUpdates.name = updates.name;
      if (updates.email !== undefined) profileUpdates.email = updates.email;
      if (updates.role !== undefined) profileUpdates.role = updates.role;
      if (updates.avatarUrl !== undefined) profileUpdates.avatar_url = updates.avatarUrl;

      const { error } = await supabase
        .from('profiles')
        .update(profileUpdates)
        .eq('id', userId);

      if (error) {
        console.error('❌ [UserService] Error updating user:', error);
        return null;
      }

      // Fetch updated profile
      const { data: profile, error: fetchError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (fetchError || !profile) {
        console.error('❌ [UserService] Error fetching updated profile:', fetchError);
        return null;
      }

      console.log('🔍 [UserService] User updated successfully');

      return {
        id: profile.id,
        name: profile.name || '',
        email: profile.email || '',
        role: profile.role || 'user',
        avatarUrl: profile.avatar_url || '',
        createdAt: new Date(profile.created_at),
        lastActive: profile.last_active ? new Date(profile.last_active) : new Date(profile.created_at)
      };
    } catch (error) {
      console.error('❌ [UserService] updateUser error:', error);
      return null;
    }
  }

  /**
   * Delete user (mark as inactive) - Admin only
   */
  async deleteUser(userId: string): Promise<boolean> {
    try {
      console.log('🔍 [UserService] deleteUser called:', userId);
      
      // Check if user is admin
      const isAdmin = await this.isUserAdmin();
      if (!isAdmin) {
        console.error('❌ [UserService] Access denied - not admin');
        return false;
      }

      // Import ChatService to delete user's chats
      const { chatService } = await import('./chatService');
      
      // Delete all user's chats first
      const chatsDeleted = await chatService.deleteAllUserChats(userId);
      if (!chatsDeleted) {
        console.error('❌ [UserService] Failed to delete user chats');
        return false;
      }

      console.log('✅ [UserService] User chats deleted successfully');

      // Mark user as inactive
      const { error } = await supabase
        .from('profiles')
        .update({ role: 'inactive' })
        .eq('id', userId);

      if (error) {
        console.error('❌ [UserService] Error marking user as inactive:', error);
        return false;
      }

      console.log('✅ [UserService] User marked as inactive');
      return true;
    } catch (error) {
      console.error('❌ [UserService] deleteUser error:', error);
      return false;
    }
  }

  /**
   * Delete current user's own account (self-deletion)
   */
  async deleteMyAccount(): Promise<boolean> {
    try {
      console.log('🔍 [UserService] deleteMyAccount called');
      
      const { data: { session } } = await supabase.auth.getSession();
      const user = session?.user;
      
      if (!user) {
        console.error('❌ [UserService] No authenticated user for account deletion');
        return false;
      }

      console.log('🔍 [UserService] Deleting account for user:', user.id);

      // Import ChatService to delete user's chats
      const { chatService } = await import('./chatService');
      
      // Delete all user's chats first
      const chatsDeleted = await chatService.deleteAllUserChats(user.id);
      if (!chatsDeleted) {
        console.error('❌ [UserService] Failed to delete user chats during account deletion');
        // Continue anyway - we still want to delete the account
      } else {
        console.log('✅ [UserService] User chats deleted during account deletion');
      }

      // Delete the user's profile
      const { error: profileError } = await supabase
        .from('profiles')
        .delete()
        .eq('id', user.id);

      if (profileError) {
        console.error('❌ [UserService] Error deleting user profile:', profileError);
        return false;
      }

      console.log('✅ [UserService] User profile deleted');

      // Sign out the user
      const { error: signOutError } = await supabase.auth.signOut();
      if (signOutError) {
        console.error('❌ [UserService] Error signing out after account deletion:', signOutError);
        // Don't return false here - account is already deleted
      }

      console.log('✅ [UserService] Account deleted successfully');
      return true;
    } catch (error) {
      console.error('❌ [UserService] deleteMyAccount error:', error);
      return false;
    }
  }
}

// Export singleton instance
export const userService = UserService.getInstance();