
import React, { useState, useCallback, useEffect } from "react";
import Sidebar from "./components/Sidebar";
import MainArea from "./components/MainArea";
import SettingsPage from "./components/SettingsPage";
import SignInModal from "./components/SignInModal";
import SignInEmailModal from "./components/SignInEmailModal";
import SignUpEmailModal from "./components/SignUpEmailModal";
import EmailVerificationModal from "./components/EmailVerificationModal";
import ResetPasswordModal from "./components/ResetPasswordModal";
import ChatHistoryAccessModal from "./components/ChatHistoryAccessModal";
import AccountModal from "./components/AccountModal";
import { supabase } from "./lib/supabase";
import { resetChatSession } from "./services/geminiService";
import { dataService } from "./services/dataService";
import { connectionService } from "./services/connectionService";
import { userService } from "./services/userService";
import {
  AuthView,
  ChatSession,
  User,
  Message,
  RawChatSession,
  UserProfiles,
  SettingsSubView as AppSettingsSubView,
} from "./types";
import { getCurrentUser } from "./constants";
import { useTranslation } from "react-i18next";
import { Routes, Route } from "react-router-dom";
import ResetPasswordPage from "./components/ResetPasswordPage";
import ResetPasswordSuccessPage from "./components/ResetPasswordSuccessPage";
import ResetPasswordDebug from "./components/ResetPasswordDebug";

// Global set to track saved message IDs and prevent duplicates
const savedMessageIds = new Set<string>();
import UserManagementPage from "./components/UserManagementPage";

export type View = "chat" | "settings" | "userManagement";

function App() {
  const [showResetPassword, setShowResetPassword] = useState(false);
  const [resetPasswordError, setResetPasswordError] = useState<string | null>(null);

  const handleForgotPassword = () => {
    setShowSignInEmail(false);
    setShowResetPassword(true);
    setResetPasswordError(null);
  };

  const handleSendResetPassword = async (email: string): Promise<void> => {
    setResetPasswordError(null);
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`
    });
    if (error) {
      setResetPasswordError(error.message);
      throw new Error(error.message);
    }
    // Success - no error thrown
  };

  const handleBackToSignIn = () => {
    setShowResetPassword(false);
    setShowSignInEmail(true);
    setResetPasswordError(null);
  };
  const { t } = useTranslation();
  // UI and navigation state
  type Theme = "light" | "dark" | "system";
  type SettingsSubView = "main" | "account";
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [isSmallScreen, setIsSmallScreen] = useState(window.innerWidth < 768);
  const [activeChatId, setActiveChatId] = useState<string | null>(null);
  const [currentView, setCurrentView] = useState<View>("chat");
  const [settingsSubView, setSettingsSubView] = useState<SettingsSubView>("main");
  // Auth modal flow state
  const [activeModal, setActiveModal] = useState<AuthView>(null);
  const [showSignInEmail, setShowSignInEmail] = useState(false);
  const [showSignUpEmail, setShowSignUpEmail] = useState(false);
  const [showEmailVerification, setShowEmailVerification] = useState(false);
  const [authEmail, setAuthEmail] = useState("");
  const [authError, setAuthError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Close all auth modals when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      setShowSignInEmail(false);
      setShowSignUpEmail(false);
      setShowEmailVerification(false);
      setActiveModal(null);
      setAuthEmail("");
      setAuthError(null);
    }
  }, [isAuthenticated]);

  // Open SignInModal from button
  const openSignInModal = useCallback(() => {
    setActiveModal("signIn");
    setShowSignInEmail(false);
    setShowSignUpEmail(false);
    setShowEmailVerification(false);
    setAuthEmail("");
    setAuthError(null);
  }, []);

  // Continue with Email from SignInModal
  const handleContinueWithEmail = () => {
    setShowSignInEmail(true);
    setActiveModal(null);
    setShowSignUpEmail(false);
    setShowEmailVerification(false);
    setAuthError(null);
  };

  // Go to SignUpEmailModal from SignInEmailModal
  const handleSignUpLink = () => {
    setShowSignInEmail(false);
    setShowSignUpEmail(true);
    setShowEmailVerification(false);
    setAuthError(null);
  };

  // Go to SignInEmailModal from SignUpEmailModal
  const handleSignInLink = () => {
    setShowSignInEmail(true);
    setShowSignUpEmail(false);
    setShowEmailVerification(false);
    setAuthError(null);
  };

  // Handle sign in with email/password
  const handleSignInEmail = async (email: string, password: string) => {
    setAuthError(null);
    const { error } = await supabase.auth.signInWithPassword({ email, password });
    if (error) {
      setAuthError(error.message);
    } else {
      setShowSignInEmail(false);
      setAuthEmail("");
      setActiveModal(null);
    }
  };

  // Handle sign up with email/password
  const handleSignUpEmail = async (email: string, password: string) => {
    setAuthError(null);
    const { error } = await supabase.auth.signUp({ email, password });
    if (error) {
      setAuthError(error.message);
    } else {
      setShowSignUpEmail(false);
      setShowEmailVerification(true);
      setAuthEmail(email);
    }
  };

  // Close all auth modals
  const closeAllAuthModals = () => {
    setShowSignInEmail(false);
    setShowSignUpEmail(false);
    setShowEmailVerification(false);
    setShowResetPassword(false);
    setAuthEmail("");
    setAuthError(null);
    setActiveModal(null);
  };
  const [isAccountModalOpen, setIsAccountModalOpen] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);

  const [userProfiles, setUserProfiles] = useState<UserProfiles | null>(null);
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [dataLoading, setDataLoading] = useState(true);
  const [dataError, setDataError] = useState<string | null>(null);

  const [theme, setTheme] = useState<Theme>(() => {
    const storedTheme = localStorage.getItem("theme") as Theme | null;
    return storedTheme || "system";
  });

  useEffect(() => {
    // Helper function to apply the actual 'dark' or 'light' mode to the DOM
    const applyModeToDOM = (userPreference: Theme) => {
      let mode: "light" | "dark";
      if (userPreference === "system") {
        mode = window.matchMedia("(prefers-color-scheme: dark)").matches
          ? "dark"
          : "light";
      } else {
        mode = userPreference;
      }

      if (mode === "dark") {
        document.documentElement.classList.add("dark");
      } else {
        document.documentElement.classList.remove("dark");
      }
    };

    // Apply the theme when the component mounts or when the 'theme' state changes
    localStorage.setItem("theme", theme); // Save user's explicit preference
    applyModeToDOM(theme); // Apply to DOM based on this preference

    // Listener for system preference changes
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    const handleSystemChange = () => {
      // Only re-apply if the user's current preference is 'system'
      if (theme === "system") {
        applyModeToDOM("system");
      }
    };

    mediaQuery.addEventListener("change", handleSystemChange);

    // Cleanup listener on component unmount or before effect re-runs
    return () => mediaQuery.removeEventListener("change", handleSystemChange);
  }, [theme]); // Re-run this effect when the user's theme preference changes

  // Initialize connection monitoring and auth state monitoring
  useEffect(() => {
    console.log('🔍 [App] Starting connection monitoring and auth state monitoring');

    // Initialize connection monitoring
    connectionService.startHealthMonitoring();

    // Initialize auth state monitoring for cache management
    userService.initializeAuthStateMonitoring();

    // Force an initial connection check
    connectionService.checkConnection().then(isConnected => {
      console.log(`🔍 [App] Initial connection check: ${isConnected ? 'Connected' : 'Not connected'}`);
    });

    // Set up periodic connection checks
    const intervalId = setInterval(() => {
      connectionService.checkConnection();
    }, 60000); // Check every minute

    return () => clearInterval(intervalId);
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      console.log('🔍 [App] Starting initial data fetch...');
      setDataLoading(true);
      setDataError(null);

      // Test Supabase connection first
      try {
        console.log('🔍 [App] Testing Supabase connection...');
        const { data, error } = await supabase.from('profiles').select('count').limit(1);
        if (error) {
          console.error('❌ [App] Supabase connection test failed:', error);
          setDataError('Database connection failed. Please check your internet connection.');
          setDataLoading(false);
          return;
        }
        console.log('✅ [App] Supabase connection test successful');
      } catch (error) {
        console.error('❌ [App] Supabase connection test error:', error);
        setDataError('Database connection failed. Please check your internet connection.');
        setDataLoading(false);
        return;
      }

      // Add a timeout to prevent infinite loading
      const timeoutId = setTimeout(() => {
        console.error('❌ [App] Data fetch timeout after 10 seconds');
        setDataError('Data loading timed out. Please refresh the page or clear your browser cache.');
        setDataLoading(false);
      }, 10000);

      try {
        // Check authentication status
        console.log('🔍 [App] Checking authentication status...');
        let isAuth;
        try {
          isAuth = await dataService.isAuthenticated();
          console.log('🔍 [App] Authentication status:', isAuth);
        } catch (err) {
          console.error('❌ [App] Error in dataService.isAuthenticated:', err);
          throw err;
        }
        setIsAuthenticated(isAuth);

        // Fetch user profiles
        let userProfilesData;
        try {
          console.log('🔍 [App] Fetching user profiles...');
          userProfilesData = await dataService.getUserProfiles();
          console.log('🔍 [App] User profiles data:', !!userProfilesData);
        } catch (err) {
          console.error('❌ [App] Error in dataService.getUserProfiles:', err);
          throw err;
        }

        // Fetch chat sessions
        let chatSessionsData;
        try {
          console.log('🔍 [App] Fetching chat sessions...');
          chatSessionsData = await dataService.getChatSessions();
          console.log('🔍 [App] Chat sessions data:', chatSessionsData?.length || 0);
        } catch (err) {
          console.error('❌ [App] Error in dataService.getChatSessions:', err);
          throw err;
        }

        if (userProfilesData) {
          setUserProfiles(userProfilesData);
          // Check if current user is admin
          const adminStatus = userProfilesData.loggedInUser?.role === 'admin';
          console.log('🔍 [App] Admin status:', adminStatus, 'User role:', userProfilesData.loggedInUser?.role);
          setIsAdmin(adminStatus);
        }
        setChatSessions(chatSessionsData);
        console.log('✅ [App] Initial data fetch completed successfully');
        clearTimeout(timeoutId);
      } catch (error) {
        console.error("❌ [App] Error fetching initial data:", error);
        setDataError(
          error instanceof Error ? error.message : "An unknown error occurred",
        );
        clearTimeout(timeoutId);
      } finally {
        console.log('🔍 [App] Setting dataLoading to false');
        setDataLoading(false);
      }
    };

    fetchData();

    // Listen for auth state changes
    const { data: { subscription } } = dataService.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session?.user?.email);
      setIsAuthenticated(!!session);

      if (session) {
        // User signed in, refresh data
        const [userProfilesData, chatSessionsData] = await Promise.all([
          dataService.getUserProfiles(),
          dataService.getChatSessions(),
        ]);

        if (userProfilesData) {
          setUserProfiles(userProfilesData);
          // Check if current user is admin
          const adminStatus = userProfilesData.loggedInUser?.role === 'admin';
          console.log('🔍 [App] Auth change - Admin status:', adminStatus, 'User role:', userProfilesData.loggedInUser?.role);
          setIsAdmin(adminStatus);
        }
        setChatSessions(chatSessionsData);
      } else {
        // User signed out, robustly clear all relevant state (do NOT fetch or set userProfiles/chatSessions)
        setIsAuthenticated(false);
        setIsAdmin(false);
        setUserProfiles(null);
        setChatSessions([]);
        setActiveChatId(null);
        resetChatSession();
        setCurrentView("chat");
        setActiveModal(null);
        setIsAccountModalOpen(false);
        setSettingsSubView("main");
        setShowSignInEmail(false);
        setShowSignUpEmail(false);
        setShowEmailVerification(false);
        setAuthEmail("");
        setAuthError(null);
        setIsSidebarOpen(false);
      }
    });

    return () => {
      subscription?.unsubscribe();
    };
  }, []);

  useEffect(() => {
    const handleResize = () => {
      const smallScreen = window.innerWidth < 768;
      setIsSmallScreen(smallScreen);
      if (isAuthenticated) {
        if (window.innerWidth >= 768) {
          setIsSidebarOpen(true);
        } else {
          setIsSidebarOpen(false);
        }
      }
    };
    window.addEventListener("resize", handleResize);
    handleResize();
    return () => window.removeEventListener("resize", handleResize);
  }, [isAuthenticated]);

  useEffect(() => {
    if (!isAuthenticated) {
      setIsSidebarOpen(false);
    } else {
      setIsSidebarOpen(window.innerWidth >= 768);
    }
  }, [isAuthenticated]);

  const handleLogin = useCallback(async () => {
    // Authentication state will be handled by the auth state change listener
    setActiveModal(null);
    setCurrentView("chat");
    setSettingsSubView("main");
  }, []);

  const handleLogout = useCallback(async () => {
    try {
      await dataService.signOut();
      // Authentication state will be handled by the auth state change listener
      setActiveChatId(null);
      resetChatSession();
      setCurrentView("chat");
      setActiveModal(null);
      setIsAccountModalOpen(false);
      setSettingsSubView("main");
    } catch (error) {
      console.error('Error signing out:', error);
    }
  }, []);


  const openChatHistoryAccessModal = useCallback(() => {
    setActiveModal("chatHistoryAccess");
  }, []);

  const closeModal = useCallback(() => {
    setActiveModal(null);
  }, []);

  const sidebarToggleHandler = useCallback(() => {
    if (!isAuthenticated) {
      openChatHistoryAccessModal();
    } else {
      setIsSidebarOpen((prev) => !prev);
    }
  }, [isAuthenticated, openChatHistoryAccessModal]);

  const navigateTo = useCallback(
    (view: View, subView: SettingsSubView = "main") => {
      setCurrentView(view);
      setSettingsSubView(subView);
      if (isAuthenticated && isSmallScreen) {
        if (view === "settings" || view === "chat") {
          setIsSidebarOpen(false);
        }
      }
    },
    [isSmallScreen, isAuthenticated],
  );

  const handleNewChat = useCallback(() => {
    setActiveChatId(null);
    resetChatSession();
    setCurrentView("chat");
    setSettingsSubView("main");
    if (isAuthenticated && isSmallScreen) {
      setIsSidebarOpen(false);
    }
  }, [isSmallScreen, isAuthenticated]);

  const handleSelectChat = useCallback(
    async (chatId: string) => {
      console.log('🔍 [handleSelectChat] Chat selected:', chatId);

      if (!isAuthenticated) {
        console.log('🔍 [handleSelectChat] User not authenticated, opening modal');
        openChatHistoryAccessModal();
        return;
      }

      resetChatSession();
      setActiveChatId(chatId);
      setCurrentView("chat");
      setSettingsSubView("main");
      if (isSmallScreen) {
        setIsSidebarOpen(false);
      }

      // Ensure database connection is healthy before loading messages
      const connectionReady = await connectionService.checkConnection();
      if (!connectionReady) {
        console.log('⚠️ [handleSelectChat] Database connection not ready, retrying...');
        await connectionService.ensureConnection();
      }

      // Load messages for the selected chat if they haven't been loaded yet
      const selectedSession = chatSessions.find(s => s.id === chatId);
      console.log('🔍 [handleSelectChat] Selected session:', selectedSession?.title, 'Messages count:', selectedSession?.messages.length);

      if (selectedSession && selectedSession.messages.length === 0) {
        try {
          console.log('🔍 [handleSelectChat] Loading messages for chat:', chatId);

          // Use retry logic for loading messages
          let retryCount = 0;
          let messages = [];

          while (retryCount < 3) {
            try {
              messages = await dataService.getMessagesForSession(chatId);
              break; // Success, exit retry loop
            } catch (err) {
              retryCount++;
              console.log(`⚠️ [handleSelectChat] Retry ${retryCount}/3 loading messages`);
              await new Promise(resolve => setTimeout(resolve, 1000)); // Wait before retry
            }
          }

          if (messages.length > 0) {
            setChatSessions(prevSessions =>
              prevSessions.map(session =>
                session.id === chatId
                  ? { ...session, messages }
                  : session
              )
            );
            console.log('✅ [handleSelectChat] Messages loaded for chat:', chatId, 'Count:', messages.length);
          } else {
            console.error('❌ [handleSelectChat] Failed to load messages after retries');
          }
        } catch (error) {
          console.error('❌ [handleSelectChat] Error loading messages:', error);
        }
      } else if (selectedSession) {
        console.log('🔍 [handleSelectChat] Messages already loaded for this chat');
      }
    },
    [isSmallScreen, isAuthenticated, openChatHistoryAccessModal, chatSessions],
  );

  const handleEditChat = useCallback(async (chatId: string, newTitle: string) => {
    try {
      console.log('🔍 [handleEditChat] Editing chat:', chatId, 'New title:', newTitle);

      const success = await dataService.updateChatTitle(chatId, newTitle);
      if (success) {
        setChatSessions(prevSessions =>
          prevSessions.map(session =>
            session.id === chatId
              ? { ...session, title: newTitle }
              : session
          )
        );
        console.log('✅ [handleEditChat] Chat title updated successfully');
      } else {
        console.error('❌ [handleEditChat] Failed to update chat title');
      }
    } catch (error) {
      console.error('❌ [handleEditChat] Error updating chat title:', error);
    }
  }, []);

  const handleDeleteChat = useCallback(async (chatId: string) => {
    try {
      console.log('🔍 [handleDeleteChat] Deleting chat:', chatId);

      const success = await dataService.deleteChatSession(chatId);
      if (success) {
        setChatSessions(prevSessions =>
          prevSessions.filter(session => session.id !== chatId)
        );

        // If the deleted chat was active, clear the active chat
        if (activeChatId === chatId) {
          setActiveChatId(null);
          resetChatSession();
        }

        console.log('✅ [handleDeleteChat] Chat deleted successfully');
      } else {
        console.error('❌ [handleDeleteChat] Failed to delete chat');
      }
    } catch (error) {
      console.error('❌ [handleDeleteChat] Error deleting chat:', error);
    }
  }, [activeChatId]);

  const updateChatSession = useCallback(
    (
      chatId: string,
      messages: Message[],
      details?: { isNew?: boolean; title?: string; skipSave?: boolean },
    ) => {
      const now = new Date();

      // Only save to database when explicitly needed (not during streaming updates)
      // Simplified save logic: save unless explicitly told to skip
      const shouldSaveToDatabase = !details?.skipSave;

      console.log('🔍 [updateChatSession] Save decision:', {
        skipSave: details?.skipSave,
        isNew: details?.isNew,
        hasUnsavedMessages: messages.some(msg => !msg.isLoading && !savedMessageIds.has(msg.id)),
        shouldSave: shouldSaveToDatabase,
        messageCount: messages.length,
        savedMessageIdsCount: savedMessageIds.size
      });

      // Check connection status before attempting to save
      if (shouldSaveToDatabase) {
        connectionService.checkConnection().then(isConnected => {
          if (!isConnected) {
            console.log('⚠️ [updateChatSession] Database connection not ready, will ensure connection before saving');
            connectionService.ensureConnection();
          }
        });
      }

      // Save to Supabase in background (non-blocking) only when needed
      if (shouldSaveToDatabase) {
        (async () => {
          try {
            // Check if user is authenticated
            const isAuth = await dataService.isAuthenticated();
            console.log('🔍 [updateChatSession] Authentication check:', isAuth);
            console.log('🔍 [updateChatSession] Chat ID:', chatId, 'Messages count:', messages.length);

            if (isAuth) {
              // Save to Supabase
              if (details?.isNew) {
                console.log('🔍 [updateChatSession] Creating new chat session...');
                // Create new chat session in Supabase
                const newSession = await dataService.createChatSession(
                  details.title || `Chat ${now.toLocaleTimeString()}`,
                  "SpeechBubbleIcon"
                );
                console.log('🔍 [updateChatSession] New session created:', newSession);

                if (newSession) {
                  // Update the chatId to use the Supabase-generated UUID
                  const supabaseChatId = newSession.id;

                  // Save all messages to Supabase (excluding streaming ones)
                  const messagesToSave = messages.filter(msg => !msg.isLoading && !savedMessageIds.has(msg.id));
                  console.log('🔍 [updateChatSession] Saving messages to Supabase:', messagesToSave.length, '(excluding streaming and already saved)');

                  for (const message of messagesToSave) {
                    try {
                      console.log('🔍 [updateChatSession] Saving message:', message.id, message.text.substring(0, 50));
                      const savedMessage = await dataService.addMessage(supabaseChatId, {
                        ...message,
                        chatId: supabaseChatId
                      });
                      console.log('🔍 [updateChatSession] Message saved successfully:', savedMessage?.id);

                      // Track this message as saved to prevent duplicates
                      savedMessageIds.add(message.id);
                    } catch (msgError) {
                      console.error('❌ Error saving message:', msgError);
                    }
                  }

                  // Update local state with Supabase chat ID
                  setChatSessions((prevSessions) => {
                    const updatedSession: ChatSession = {
                      ...newSession,
                      messages: messages.map(msg => ({ ...msg, chatId: supabaseChatId }))
                    };
                    console.log('🔍 [updateChatSession] Updated local state with Supabase chat ID:', supabaseChatId);
                    return [updatedSession, ...prevSessions.filter((s) => s.id !== chatId && s.id !== supabaseChatId)];
                  });

                  // Update active chat ID to the Supabase ID
                  setActiveChatId(supabaseChatId);
                  return;
                }
              } else if (!chatId.startsWith('temp-')) {
                // Only update existing chat session if it's a real UUID (not temporary)
                console.log('🔍 [updateChatSession] Updating existing chat session:', chatId);
                // Find new messages that need to be saved (skip streaming/loading messages)
                const existingSession = chatSessions.find(s => s.id === chatId);
                const existingMessageIds = new Set(existingSession?.messages.map(m => m.id) || []);

                // Simple approach: Save any message that's complete and not already saved
                const newMessages = messages.filter(msg => {
                  const isNotStreaming = !msg.isLoading;
                  const isNotSaved = !msg.savedToDatabase && !savedMessageIds.has(msg.id);
                  const isNew = !existingMessageIds.has(msg.id);

                  // For existing messages, check if they were streaming and now complete
                  const existingMsg = existingSession?.messages.find(m => m.id === msg.id);
                  const wasStreamingNowComplete = existingMsg?.isLoading === true && msg.isLoading === false;

                  const shouldSave = (isNew && isNotStreaming && isNotSaved) || (wasStreamingNowComplete && isNotSaved);

                  if (shouldSave) {
                    console.log('🔍 [updateChatSession] Will save message:', msg.id, msg.sender, msg.isLoading ? 'streaming' : 'complete', msg.text?.substring(0, 30));
                  }

                  return shouldSave;
                });

                console.log('🔍 [updateChatSession] Messages to save:', newMessages.length);

                // Save new messages to Supabase (filter out already saved)
                const unsavedMessages = newMessages.filter(msg => !savedMessageIds.has(msg.id));
                console.log('🔍 [updateChatSession] Filtered unsaved messages:', unsavedMessages.length, 'of', newMessages.length);

                for (const message of unsavedMessages) {
                  try {
                    console.log('🔍 [updateChatSession] Saving new message to existing chat:', message.id);
                    const savedMessage = await dataService.addMessage(chatId, message);
                    console.log('🔍 [updateChatSession] Message saved to existing chat:', savedMessage?.id);

                    // Track this message as saved to prevent duplicates
                    savedMessageIds.add(message.id);
                  } catch (msgError) {
                    console.error('❌ Error saving message to existing chat:', msgError);
                  }
                }

                // Handle completed streaming messages (when isLoading becomes false)
                const completedStreamingMessages = messages.filter(msg => {
                  const existingMsg = existingSession?.messages.find(m => m.id === msg.id);
                  const wasStreaming = existingMsg?.isLoading === true;
                  const nowComplete = msg.isLoading === false;
                  const notSaved = !msg.savedToDatabase;

                  if (wasStreaming && nowComplete && notSaved) {
                    console.log('🔍 [updateChatSession] Found completed streaming message:', msg.id, msg.text?.substring(0, 50));
                    return true;
                  }

                  // Also check for AI messages that are complete but not saved
                  if (msg.sender === 'ai' && !msg.isLoading && !savedMessageIds.has(msg.id)) {
                    console.log('🔍 [updateChatSession] Found unsaved AI message:', msg.id, msg.text?.substring(0, 50));
                    return true;
                  }

                  return false;
                });

                // Filter out already saved completed streaming messages
                const unsavedCompletedMessages = completedStreamingMessages.filter(msg => !savedMessageIds.has(msg.id));
                console.log('🔍 [updateChatSession] Filtered unsaved completed messages:', unsavedCompletedMessages.length, 'of', completedStreamingMessages.length);

                for (const message of unsavedCompletedMessages) {
                  try {
                    console.log('🔍 [updateChatSession] Saving completed streaming message:', message.id);
                    const savedMessage = await dataService.addMessage(chatId, message);
                    console.log('🔍 [updateChatSession] Completed streaming message saved:', savedMessage?.id);

                    // Track this message as saved to prevent duplicates
                    if (savedMessage) {
                      savedMessageIds.add(message.id);
                      console.log('🔍 [updateChatSession] Added message ID to savedMessageIds:', message.id);
                    }
                  } catch (msgError) {
                    console.error('❌ Error saving completed streaming message:', msgError);
                  }
                }
              } else {
                console.log('🔍 [updateChatSession] Skipping database save for temporary chat ID:', chatId, 'but will update local state');
              }
            } else {
              console.log('🔍 [updateChatSession] User not authenticated, skipping Supabase save');
            }
          } catch (error) {
            console.error('❌ Error in updateChatSession:', error);
            // Continue with local state update even if Supabase fails
          }
        })();
      }

      // Update local state (always update local state for UI responsiveness)
      console.log('🔍 [updateChatSession] Updating local state for chat:', chatId, 'isNew:', details?.isNew, 'messages:', messages.length);
      setChatSessions((prevSessions) => {
        if (details?.isNew) {
          const newSession: ChatSession = {
            id: chatId,
            title: details.title || `Chat ${now.toLocaleTimeString()}`,
            messages: messages,
            lastActivity: now,
            iconName: "SpeechBubbleIcon",
          };
          return [newSession, ...prevSessions.filter((s) => s.id !== chatId)];
        }
        const updatedSessions = prevSessions
          .map((session) => {
            if (session.id === chatId) {
              console.log('🔍 [updateChatSession] Updating existing session:', chatId, 'with', messages.length, 'messages');
              return { ...session, messages: messages, lastActivity: now };
            }
            return session;
          })
          .sort((a, b) => b.lastActivity.getTime() - a.lastActivity.getTime());

        console.log('🔍 [updateChatSession] Local state updated for chat:', chatId);
        return updatedSessions;
      });
    },
    [chatSessions, setActiveChatId],
  );

  const openAccountModalHandler = () => {
    if (isSmallScreen) {
      navigateTo("settings", "account");
    } else {
      setIsAccountModalOpen(true);
    }
  };

  const closeAccountModalHandler = () => {
    if (isSmallScreen) {
      navigateTo("settings", "main");
    } else {
      setIsAccountModalOpen(false);
    }
  };

  const handleSaveAccountChanges = (updatedUserData: Partial<User>) => {
    if (userProfiles) {
      setUserProfiles((prevProfiles) => {
        if (!prevProfiles) return null;
        return {
          ...prevProfiles,
          loggedInUser: {
            ...prevProfiles.loggedInUser,
            ...updatedUserData,
          },
        };
      });
    }
    closeAccountModalHandler();
    console.log("Account changes saved (simulated):", updatedUserData);
  };

  const exportData = () => {
    const chatsToExport = chatSessions.map((session) => ({
      ...session,
      lastActivity: session.lastActivity.toISOString(),
      icon: undefined,
    }));
    const chatsJsonString = JSON.stringify(chatsToExport, null, 2);
    const chatsBlob = new Blob([chatsJsonString], { type: "application/json" });
    const chatsUrl = URL.createObjectURL(chatsBlob);
    const chatsLink = document.createElement("a");
    chatsLink.href = chatsUrl;
    chatsLink.download = "chats_updated.json";
    document.body.appendChild(chatsLink);
    chatsLink.click();
    document.body.removeChild(chatsLink);
    URL.revokeObjectURL(chatsUrl);

    if (userProfiles) {
      console.log(
        "Exporting userProfiles state:",
        JSON.parse(JSON.stringify(userProfiles)),
      );
      const usersJsonString = JSON.stringify(userProfiles, null, 2);
      const usersBlob = new Blob([usersJsonString], {
        type: "application/json",
      });
      const usersUrl = URL.createObjectURL(usersBlob);
      const usersLink = document.createElement("a");
      usersLink.href = usersUrl;
      usersLink.download = "users_updated.json";
      document.body.appendChild(usersLink);
      usersLink.click();
      document.body.removeChild(usersLink);
      URL.revokeObjectURL(usersUrl);
    }
  };

  const currentUser = getCurrentUser(isAuthenticated, userProfiles);

  // Debug: Log current state
  console.log('🔍 [App] Current state:', {
    isAuthenticated,
    isAdmin,
    currentUser,
    userRole: userProfiles?.loggedInUser?.role
  });

  if (dataLoading) {
    return (
      <div
        className="flex items-center justify-center h-screen text-slate-700 dark:text-slate-300 text-lg bg-white dark:bg-slate-900"
        data-oid="sh1r8me"
      >
        Loading application data...
      </div>
    );
  }

  if (dataError) {
    return (
      <div
        className="flex flex-col items-center justify-center h-screen text-red-600 dark:text-red-400 p-4 bg-white dark:bg-slate-900"
        data-oid="oz-qi0r"
      >
        <h1 className="text-xl font-semibold mb-2" data-oid="3k33w3o">
          Error Loading Data
        </h1>
        <p className="text-center" data-oid="13.r9tx">
          {dataError}
        </p>
        <p
          className="mt-4 text-sm text-slate-500 dark:text-slate-400"
          data-oid="tpaoadn"
        >
          Please try refreshing the page or check the console for more details.
        </p>
      </div>
    );
  }

  const accountModalToShow =
    (currentView === "settings" &&
      settingsSubView === "account" &&
      isSmallScreen) ||
    (isAccountModalOpen && !isSmallScreen);

  return (
    <>
      <Routes>
        <Route path="/reset-password" element={<ResetPasswordPage />} />
        <Route path="/reset-password-success" element={<ResetPasswordSuccessPage />} />
        <Route path="/reset-password-debug" element={<ResetPasswordDebug />} />
        <Route path="*" element={
          <div
            className="flex h-screen overflow-hidden bg-slate-100 dark:bg-slate-900"
            data-oid="nj:ly4f"
          >
            {isAuthenticated &&
              isSmallScreen &&
              isSidebarOpen &&
              currentView === "chat" && (
                <div
                  className="fixed inset-0 bg-black/50 dark:bg-black/70 z-40 transition-opacity duration-300 ease-in-out"
                  onClick={() => setIsSidebarOpen(false)}
                  aria-hidden="true"
                  data-oid="q9u.v8q"
                />
              )}
            {/* ...existing code... (rest of App render remains unchanged) */}
            <Sidebar
              chatSessions={chatSessions}
              onNewChat={handleNewChat}
              onSelectChat={handleSelectChat}
              activeChatId={activeChatId}
              isSidebarOpen={isSidebarOpen}
              onToggleSidebar={sidebarToggleHandler}
              isSmallScreen={isSmallScreen}
              navigateTo={navigateTo}
              currentView={currentView}
              isAuthenticated={isAuthenticated}
              currentUser={currentUser}
              handleLogout={handleLogout}
              openSignInModal={openSignInModal}
              isAdmin={isAdmin}
              onEditChat={handleEditChat}
              onDeleteChat={handleDeleteChat}
              data-oid="7-c5ox5"
            />
            {/* ...existing code... (rest of App render remains unchanged) */}
            {currentView === "chat" ? (
              <MainArea
                activeChatId={activeChatId}
                setActiveChatId={setActiveChatId}
                chatMessages={
                  chatSessions.find((cs) => cs.id === activeChatId)?.messages || []
                }
                onUpdateChat={updateChatSession}
                isSidebarOpen={isSidebarOpen}
                onToggleSidebar={sidebarToggleHandler}
                onNewChat={handleNewChat}
                navigateTo={navigateTo}
                isAuthenticated={isAuthenticated}
                currentUser={currentUser}
                handleLogout={handleLogout}
                openSignInModal={openSignInModal}
                data-oid="8z2z0f3"
              />
            ) : currentView === "settings" && settingsSubView === "main" ? (
              <SettingsPage
                navigateTo={navigateTo}
                isSmallScreen={isSmallScreen}
                openAccountModal={openAccountModalHandler}
                settingsSubView={settingsSubView}
                currentTheme={theme}
                onThemeChange={setTheme}
                data-oid="jy8:kpe"
              />
            ) : currentView === "userManagement" ? (
              <UserManagementPage
                navigateTo={navigateTo}
              />
            ) : null}
            {accountModalToShow && (
              <AccountModal
                isOpen={true}
                onClose={closeAccountModalHandler}
                onSave={handleSaveAccountChanges}
                currentUser={currentUser}
                isSmallScreen={isSmallScreen}
                data-oid="l8n6jc2"
              />
            )}
            {activeModal === "signIn" && (
              <SignInModal
                isOpen={true}
                onClose={closeAllAuthModals}
                onSignIn={handleLogin}
                onContinueWithEmail={handleContinueWithEmail}
                data-oid="8gszv0w"
              />
            )}
            {showSignInEmail && (
              <SignInEmailModal
                isOpen={true}
                onClose={closeAllAuthModals}
                onSignUp={handleSignUpLink}
                onGoogleSignIn={() => supabase.auth.signInWithOAuth({ provider: "google" })}
                onGithubSignIn={() => supabase.auth.signInWithOAuth({ provider: "github" })}
                onSignIn={handleSignInEmail}
                error={authError}
                onForgotPassword={handleForgotPassword}
              />
            )}
            {showResetPassword && (
              <ResetPasswordModal
                isOpen={true}
                onClose={closeAllAuthModals}
                onBackToSignIn={handleBackToSignIn}
                onSendReset={handleSendResetPassword}
                error={resetPasswordError}
              />
            )}
            {showSignUpEmail && (
              <SignUpEmailModal
                isOpen={true}
                onClose={closeAllAuthModals}
                onSignIn={handleSignInLink}
                onGoogleSignUp={() => supabase.auth.signInWithOAuth({ provider: "google" })}
                onGithubSignUp={() => supabase.auth.signInWithOAuth({ provider: "github" })}
                onSignUp={handleSignUpEmail}
                error={authError}
              />
            )}
            {showEmailVerification && (
              <EmailVerificationModal
                isOpen={true}
                onClose={closeAllAuthModals}
                email={authEmail}
              />
            )}
            {activeModal === "chatHistoryAccess" && (
              <ChatHistoryAccessModal
                isOpen={true}
                onClose={closeModal}
                onSignInRedirect={() => {
                  closeModal();
                  openSignInModal();
                }}
                data-oid="qx1_9wx"
              />
            )}
            <div className="fixed bottom-2 right-2 z-[100]" data-oid="t.4vuuo">
              <button
                onClick={exportData}
                className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow"
                title={t(
                  "exportDataTooltip",
                  "Download chat sessions and user data as JSON",
                )}
                data-oid="yahgk.0"
              >
                {isAuthenticated
                  ? t("exportData")
                  : t("exportChatsFallback", "Export Chats")}
              </button>
            </div>
          </div>
        } />
      </Routes>
    </>
  );
}

export default App;
