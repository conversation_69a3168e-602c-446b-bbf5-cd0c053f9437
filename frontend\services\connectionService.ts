import { supabase } from '../lib/supabase';

/**
 * Connection Service - Manages database connectivity and health checks
 */
export class ConnectionService {
  private static instance: ConnectionService;
  private isConnected: boolean = false;
  private lastHealthCheck: number = 0;
  private healthCheckInterval: number = 30000; // 30 seconds
  private connectionRetryAttempts: number = 0;
  private maxRetryAttempts: number = 3;

  static getInstance(): ConnectionService {
    if (!ConnectionService.instance) {
      ConnectionService.instance = new ConnectionService();
    }
    return ConnectionService.instance;
  }

  /**
   * Check if database connection is healthy
   */
  async checkConnection(): Promise<boolean> {
    const now = Date.now();
    
    // Use cached result if recent
    if (this.isConnected && (now - this.lastHealthCheck) < this.healthCheckInterval) {
      return true;
    }

    try {
      console.log('🔍 [ConnectionService] Checking database connection...');
      
      // Simple health check query with timeout
      const healthCheckPromise = supabase
        .from('profiles')
        .select('count')
        .limit(1);
      
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Connection timeout')), 5000)
      );

      await Promise.race([healthCheckPromise, timeoutPromise]);
      
      this.isConnected = true;
      this.lastHealthCheck = now;
      this.connectionRetryAttempts = 0;
      
      console.log('✅ [ConnectionService] Database connection healthy');
      return true;
    } catch (error) {
      console.error('❌ [ConnectionService] Database connection failed:', error);
      this.isConnected = false;
      this.connectionRetryAttempts++;
      return false;
    }
  }

  /**
   * Ensure connection is ready before performing database operations
   */
  async ensureConnection(): Promise<boolean> {
    if (this.connectionRetryAttempts >= this.maxRetryAttempts) {
      console.error('❌ [ConnectionService] Max retry attempts reached');
      return false;
    }

    const isHealthy = await this.checkConnection();
    
    if (!isHealthy && this.connectionRetryAttempts < this.maxRetryAttempts) {
      console.log('🔄 [ConnectionService] Retrying connection...');
      await new Promise(resolve => setTimeout(resolve, 1000 * this.connectionRetryAttempts));
      return this.ensureConnection();
    }
    
    return isHealthy;
  }

  /**
   * Reset connection state (useful after network issues)
   */
  resetConnection(): void {
    console.log('🔄 [ConnectionService] Resetting connection state');
    this.isConnected = false;
    this.lastHealthCheck = 0;
    this.connectionRetryAttempts = 0;
  }

  /**
   * Get connection status
   */
  getConnectionStatus(): { isConnected: boolean; lastCheck: number; retryAttempts: number } {
    return {
      isConnected: this.isConnected,
      lastCheck: this.lastHealthCheck,
      retryAttempts: this.connectionRetryAttempts
    };
  }

  /**
   * Start periodic health checks
   */
  startHealthMonitoring(): void {
    console.log('🔍 [ConnectionService] Starting health monitoring');
    
    setInterval(async () => {
      await this.checkConnection();
    }, this.healthCheckInterval);
  }

  /**
   * Execute a database operation with connection retry
   */
  async executeWithRetry<T>(operation: () => Promise<T>, operationName: string): Promise<T | null> {
    try {
      // Ensure connection is ready
      const connectionReady = await this.ensureConnection();
      if (!connectionReady) {
        console.error(`❌ [ConnectionService] Cannot execute ${operationName} - connection not ready`);
        return null;
      }

      console.log(`🔍 [ConnectionService] Executing ${operationName} with healthy connection`);
      const result = await operation();
      
      // Reset retry attempts on success
      this.connectionRetryAttempts = 0;
      return result;
    } catch (error) {
      console.error(`❌ [ConnectionService] Error executing ${operationName}:`, error);
      
      // Reset connection state on error
      this.resetConnection();
      
      // Retry once if it's a connection-related error
      if (this.connectionRetryAttempts === 0 && this.isConnectionError(error)) {
        console.log(`🔄 [ConnectionService] Retrying ${operationName} after connection error`);
        this.connectionRetryAttempts++;
        return this.executeWithRetry(operation, operationName);
      }
      
      return null;
    }
  }

  /**
   * Ensure authentication session is valid
   */
  async ensureAuthSession(): Promise<boolean> {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        console.error('❌ [ConnectionService] Auth session error:', error);
        return false;
      }
      
      if (!session) {
        console.log('⚠️ [ConnectionService] No active session');
        return false;
      }
      
      // Check if session is expired
      const now = Math.floor(Date.now() / 1000);
      if (session.expires_at && session.expires_at < now) {
        console.log('⚠️ [ConnectionService] Session expired, refreshing...');
        
        const { data: { session: newSession }, error: refreshError } = await supabase.auth.refreshSession();
        
        if (refreshError || !newSession) {
          console.error('❌ [ConnectionService] Session refresh failed:', refreshError);
          return false;
        }
        
        console.log('✅ [ConnectionService] Session refreshed successfully');
      }
      
      return true;
    } catch (error) {
      console.error('❌ [ConnectionService] ensureAuthSession error:', error);
      return false;
    }
  }

  /**
   * Check if error is connection-related
   */
  private isConnectionError(error: any): boolean {
    const errorMessage = error?.message?.toLowerCase() || '';
    const connectionErrors = [
      'network',
      'timeout',
      'connection',
      'fetch',
      'cors',
      'failed to fetch'
    ];
    
    return connectionErrors.some(keyword => errorMessage.includes(keyword));
  }
}

// Export singleton instance
export const connectionService = ConnectionService.getInstance();