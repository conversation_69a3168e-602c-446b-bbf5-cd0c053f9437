import { supabase } from '../lib/supabase';

/**
 * Connection Service - Manages database connectivity and health checks
 */
export class ConnectionService {
  private static instance: ConnectionService;
  private isConnected: boolean = false;
  private lastHealthCheck: number = 0;
  private healthCheckInterval: number = 30000; // 30 seconds
  private connectionRetryAttempts: number = 0;
  private maxRetryAttempts: number = 3;
  private pendingConnectionCheck: Promise<boolean> | null = null;
  private pendingAuthCheck: Promise<boolean> | null = null;

  static getInstance(): ConnectionService {
    if (!ConnectionService.instance) {
      ConnectionService.instance = new ConnectionService();
    }
    return ConnectionService.instance;
  }

  /**
   * Check if database connection is healthy with deduplication
   */
  async checkConnection(): Promise<boolean> {
    const now = Date.now();

    // Use cached result if recent
    if (this.isConnected && (now - this.lastHealthCheck) < this.healthCheckInterval) {
      return true;
    }

    // If there's already a pending connection check, return it
    if (this.pendingConnectionCheck) {
      console.log('🔍 [ConnectionService] Returning pending connection check');
      return this.pendingConnectionCheck;
    }

    // Create new connection check and cache it
    this.pendingConnectionCheck = this.performConnectionCheck();

    try {
      const result = await this.pendingConnectionCheck;
      return result;
    } finally {
      // Clear pending check
      this.pendingConnectionCheck = null;
    }
  }

  /**
   * Internal method to perform the actual connection check
   */
  private async performConnectionCheck(): Promise<boolean> {
    try {
      console.log('🔍 [ConnectionService] Checking database connection...');

      // Simple health check query with timeout (increased to 10s)
      const healthCheckPromise = supabase
        .from('profiles')
        .select('count')
        .limit(1);

      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Connection timeout')), 10000)
      );

      await Promise.race([healthCheckPromise, timeoutPromise]);

      this.isConnected = true;
      this.lastHealthCheck = Date.now();
      this.connectionRetryAttempts = 0;

      console.log('✅ [ConnectionService] Database connection healthy');
      return true;
    } catch (error) {
      console.error('❌ [ConnectionService] Database connection failed:', error);
      this.isConnected = false;
      this.connectionRetryAttempts++;
      return false;
    }
  }

  /**
   * Ensure connection is ready before performing database operations
   */
  async ensureConnection(): Promise<boolean> {
    if (this.connectionRetryAttempts >= this.maxRetryAttempts) {
      console.error('❌ [ConnectionService] Max retry attempts reached');
      return false;
    }

    const isHealthy = await this.checkConnection();
    
    if (!isHealthy && this.connectionRetryAttempts < this.maxRetryAttempts) {
      console.log('🔄 [ConnectionService] Retrying connection...');
      await new Promise(resolve => setTimeout(resolve, 1000 * this.connectionRetryAttempts));
      return this.ensureConnection();
    }
    
    return isHealthy;
  }

  /**
   * Reset connection state (useful after network issues)
   */
  resetConnection(): void {
    console.log('🔄 [ConnectionService] Resetting connection state');
    this.isConnected = false;
    this.lastHealthCheck = 0;
    this.connectionRetryAttempts = 0;
    this.pendingConnectionCheck = null;
    this.pendingAuthCheck = null;
  }

  /**
   * Get connection status
   */
  getConnectionStatus(): { isConnected: boolean; lastCheck: number; retryAttempts: number } {
    return {
      isConnected: this.isConnected,
      lastCheck: this.lastHealthCheck,
      retryAttempts: this.connectionRetryAttempts
    };
  }

  /**
   * Start periodic health checks
   */
  startHealthMonitoring(): void {
    console.log('🔍 [ConnectionService] Starting health monitoring');
    
    setInterval(async () => {
      await this.checkConnection();
    }, this.healthCheckInterval);
  }

  /**
   * Execute a database operation with connection retry
   */
  async executeWithRetry<T>(operation: () => Promise<T>, operationName: string): Promise<T | null> {
    try {
      // Ensure connection is ready
      const connectionReady = await this.ensureConnection();
      if (!connectionReady) {
        console.error(`❌ [ConnectionService] Cannot execute ${operationName} - connection not ready`);
        return null;
      }

      console.log(`🔍 [ConnectionService] Executing ${operationName} with healthy connection`);
      const result = await operation();
      
      // Reset retry attempts on success
      this.connectionRetryAttempts = 0;
      return result;
    } catch (error) {
      console.error(`❌ [ConnectionService] Error executing ${operationName}:`, error);
      
      // Reset connection state on error
      this.resetConnection();
      
      // Retry once if it's a connection-related error
      if (this.connectionRetryAttempts === 0 && this.isConnectionError(error)) {
        console.log(`🔄 [ConnectionService] Retrying ${operationName} after connection error`);
        this.connectionRetryAttempts++;
        return this.executeWithRetry(operation, operationName);
      }
      
      return null;
    }
  }

  /**
   * Ensure authentication session is valid with deduplication and enhanced error handling
   */
  async ensureAuthSession(): Promise<boolean> {
    // If there's already a pending auth check, return it
    if (this.pendingAuthCheck) {
      console.log('🔍 [ConnectionService] Returning pending auth check');
      return this.pendingAuthCheck;
    }

    // Create new auth check and cache it
    this.pendingAuthCheck = this.performAuthSessionCheck();

    try {
      const result = await this.pendingAuthCheck;
      return result;
    } finally {
      // Clear pending check
      this.pendingAuthCheck = null;
    }
  }

  /**
   * Internal method to perform the actual auth session check
   */
  private async performAuthSessionCheck(): Promise<boolean> {
    try {
      console.log('🔍 [ConnectionService] Checking auth session...');

      // Add timeout to session check
      const sessionPromise = supabase.auth.getSession();
      const sessionTimeout = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Auth session check timeout')), 10000)
      );

      const { data: { session }, error } = await Promise.race([sessionPromise, sessionTimeout]) as any;

      if (error) {
        console.error('❌ [ConnectionService] Auth session error:', error);
        return false;
      }

      if (!session) {
        console.log('⚠️ [ConnectionService] No active session');
        return false;
      }

      // Check if session is expired
      const now = Math.floor(Date.now() / 1000);
      if (session.expires_at && session.expires_at < now) {
        console.log('⚠️ [ConnectionService] Session expired, refreshing...');

        // Add timeout to refresh operation
        const refreshPromise = supabase.auth.refreshSession();
        const refreshTimeout = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Session refresh timeout')), 10000)
        );

        const { data: { session: newSession }, error: refreshError } = await Promise.race([refreshPromise, refreshTimeout]) as any;

        if (refreshError || !newSession) {
          console.error('❌ [ConnectionService] Session refresh failed:', refreshError);
          return false;
        }

        console.log('✅ [ConnectionService] Session refreshed successfully');
      }

      console.log('✅ [ConnectionService] Auth session is valid');
      return true;
    } catch (error) {
      console.error('❌ [ConnectionService] performAuthSessionCheck error:', error);
      return false;
    }
  }

  /**
   * Check if error is connection-related with enhanced logging
   */
  private isConnectionError(error: any): boolean {
    const errorMessage = error?.message?.toLowerCase() || '';
    const connectionErrors = [
      'network',
      'timeout',
      'connection',
      'fetch',
      'cors',
      'failed to fetch',
      'session check timeout',
      'auth session check timeout',
      'session refresh timeout'
    ];

    const isConnError = connectionErrors.some(keyword => errorMessage.includes(keyword));

    if (isConnError) {
      console.log('🔍 [ConnectionService] Detected connection-related error:', error?.message || error);
    }

    return isConnError;
  }

  /**
   * Enhanced logging for auth state transitions
   */
  logAuthStateTransition(from: string, to: string, details?: any): void {
    console.log(`🔄 [ConnectionService] Auth transition: ${from} → ${to}`, details || '');
  }

  /**
   * Get detailed connection and auth status for debugging
   */
  getDetailedStatus(): {
    connection: { isConnected: boolean; lastCheck: number; retryAttempts: number };
    pendingRequests: { hasConnectionCheck: boolean; hasAuthCheck: boolean };
    timestamps: { lastHealthCheck: number; now: number; timeSinceLastCheck: number };
  } {
    const now = Date.now();
    return {
      connection: {
        isConnected: this.isConnected,
        lastCheck: this.lastHealthCheck,
        retryAttempts: this.connectionRetryAttempts
      },
      pendingRequests: {
        hasConnectionCheck: !!this.pendingConnectionCheck,
        hasAuthCheck: !!this.pendingAuthCheck
      },
      timestamps: {
        lastHealthCheck: this.lastHealthCheck,
        now,
        timeSinceLastCheck: now - this.lastHealthCheck
      }
    };
  }
}

// Export singleton instance
export const connectionService = ConnectionService.getInstance();